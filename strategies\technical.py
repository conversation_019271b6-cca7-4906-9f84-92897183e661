# WQB技术分析策略模板
# 脚本功能：提供技术分析的因子表达式模板和生成逻辑

from typing import List, Dict, Any
from data.schemas import StrategyType

class TechnicalStrategyTemplates:
    """技术分析策略模板集合"""
    
    # 价格动量模板
    PRICE_MOMENTUM = [
        "ts_rank(close / ts_delay(close, 20), 60)",
        "rank(ts_sum(returns, 5) / ts_std_dev(returns, 5))",
        "zscore(close / ts_mean(close, 20) - 1)",
        "ts_rank(ts_mean(returns, 10), 252)",
        "group_neutralize(close / ts_delay(close, 5) - 1, sector)",
        "rank(ts_sum(close > ts_delay(close, 1), 20))",
        "zscore(ts_regression(close, ts_step(1), 20, 0, 1))",
        "ts_rank(close / vwap - 1, 20)",
        "rank(ts_mean(high / low - 1, 10))",
        "group_rank(ts_delta(close, 1) / ts_std_dev(close, 20), industry)"
    ]
    
    # 成交量分析模板
    VOLUME_ANALYSIS = [
        "rank(volume / ts_mean(volume, 20))",
        "zscore(ts_corr(close, volume, 10))",
        "ts_rank(volume * (close / ts_delay(close, 1) - 1), 20)",
        "group_neutralize(vwap / close - 1, subindustry)",
        "rank(ts_sum(volume * (close > open), 5) / ts_sum(volume, 5))",
        "zscore(volume / ts_mean(volume, 60))",
        "ts_rank(ts_mean(volume / ts_delay(volume, 1), 10), 60)",
        "rank(ts_corr(returns, volume / ts_mean(volume, 20), 20))",
        "group_rank(vwap / ts_mean(vwap, 10) - 1, sector)",
        "zscore(ts_sum(volume * returns, 10) / ts_sum(volume, 10))"
    ]
    
    # 波动率指标模板
    VOLATILITY_INDICATORS = [
        "rank(1 / ts_std_dev(returns, 20))",
        "zscore(ts_std_dev(close / ts_delay(close, 1), 10))",
        "ts_rank((high - low) / close, 60)",
        "group_neutralize(ts_std_dev(returns, 60), industry)",
        "rank(ts_mean((high - low) / open, 20))",
        "zscore(1 / ts_std_dev(ts_delta(close, 1), 20))",
        "ts_rank(abs(close - open) / (high - low), 20)",
        "rank(ts_std_dev(vwap / close, 10))",
        "group_rank(1 / ts_std_dev(volume / ts_mean(volume, 20), 20), sector)",
        "zscore(ts_mean(abs(returns), 20) / ts_std_dev(returns, 20))"
    ]
    
    # 均值回归模板
    MEAN_REVERSION = [
        "rank(ts_mean(close, 20) / close - 1)",
        "zscore(close / ts_mean(close, 60) - 1)",
        "ts_rank(1 - close / ts_max(close, 20), 252)",
        "group_neutralize(ts_mean(close, 10) / close - 1, subindustry)",
        "rank((ts_mean(close, 5) - close) / ts_std_dev(close, 20))",
        "zscore(vwap / close - 1)",
        "ts_rank(ts_mean(high, 10) / close - 1, 60)",
        "rank((ts_mean(low, 5) - close) / (high - low))",
        "group_rank(close / ts_max(close, 60) - 1, industry)",
        "zscore((open - ts_delay(close, 1)) / ts_std_dev(close, 20))"
    ]
    
    # 相对强度模板
    RELATIVE_STRENGTH = [
        "rank(ts_sum(close > ts_delay(close, 1), 20) / 20)",
        "zscore(ts_rank(close, 20) / 20)",
        "group_neutralize(close / ts_rank(close, 252), sector)",
        "ts_rank(ts_mean(close / ts_delay(close, 1), 10), 60)",
        "rank(close / ts_quantile(close, 60, 0.5))",
        "zscore(ts_sum(returns > 0, 20) / 20)",
        "group_rank(close / ts_mean(close, 252), industry)",
        "ts_rank(close / ts_percentile(close, 120, 0.2), 252)",
        "rank(ts_corr(close, ts_step(1), 20))",
        "zscore(close / ts_min(close, 60) - 1)"
    ]
    
    # 复合技术指标模板
    COMPOSITE_TECHNICAL = [
        "rank(ts_sum(returns, 5)) * rank(1 / ts_std_dev(returns, 20))",
        "zscore(close / ts_mean(close, 20)) * zscore(volume / ts_mean(volume, 20))",
        "group_neutralize(ts_rank(close, 20) * ts_rank(volume, 20), industry)",
        "rank(vwap / close) + rank(ts_delta(close, 1) / ts_std_dev(close, 20))",
        "ts_rank(close / ts_delay(close, 5), 20) * rank(1 / ts_std_dev(returns, 10))",
        "zscore(ts_corr(close, volume, 10)) * zscore(ts_sum(returns, 5))",
        "group_rank((high + low) / 2 / close, sector) + group_rank(volume / ts_mean(volume, 20), sector)",
        "rank(ts_regression(close, volume, 10, 0, 1)) * rank(close / vwap)",
        "ts_rank(close / open - 1, 20) + ts_rank(volume / ts_delay(volume, 1), 20)",
        "zscore((close - ts_min(close, 20)) / (ts_max(close, 20) - ts_min(close, 20))) * rank(volume)"
    ]
    
    @classmethod
    def get_all_templates(cls) -> List[str]:
        """获取所有技术分析策略模板"""
        all_templates = []
        all_templates.extend(cls.PRICE_MOMENTUM)
        all_templates.extend(cls.VOLUME_ANALYSIS)
        all_templates.extend(cls.VOLATILITY_INDICATORS)
        all_templates.extend(cls.MEAN_REVERSION)
        all_templates.extend(cls.RELATIVE_STRENGTH)
        all_templates.extend(cls.COMPOSITE_TECHNICAL)
        return all_templates
    
    @classmethod
    def get_templates_by_category(cls, category: str) -> List[str]:
        """根据类别获取模板"""
        category_map = {
            'momentum': cls.PRICE_MOMENTUM,
            'volume': cls.VOLUME_ANALYSIS,
            'volatility': cls.VOLATILITY_INDICATORS,
            'mean_reversion': cls.MEAN_REVERSION,
            'relative_strength': cls.RELATIVE_STRENGTH,
            'composite': cls.COMPOSITE_TECHNICAL
        }
        return category_map.get(category.lower(), [])
    
    @classmethod
    def get_ai_generation_prompts(cls) -> Dict[str, str]:
        """获取AI生成提示模板"""
        return {
            'basic_prompt': """基于技术分析生成量化因子表达式。要求：
1. 使用价格数据如close, open, high, low, volume, vwap等
2. 应用技术分析运算如ts_rank(), ts_corr(), ts_std_dev()等
3. 考虑动量、均值回归、波动率等技术概念
4. 使用适当的时间窗口参数如5, 10, 20, 60天
5. 确保表达式具有技术分析逻辑

请生成{count}个不同的技术分析因子表达式：""",
            
            'momentum_focused': """生成动量投资导向的技术分析因子表达式。要求：
1. 重点关注价格趋势和动量指标
2. 使用ts_rank(), ts_sum(), ts_delta()等函数
3. 结合成交量确认动量强度
4. 考虑相对强度和趋势持续性
5. 表达式应体现"买入上涨趋势强劲股票"逻辑

请生成{count}个动量投资因子表达式：""",
            
            'mean_reversion_focused': """生成均值回归导向的技术分析因子表达式。要求：
1. 重点关注价格偏离均值的程度
2. 使用ts_mean(), ts_std_dev(), zscore()等函数
3. 考虑超买超卖状态识别
4. 结合波动率调整信号强度
5. 表达式应体现"买入被过度抛售股票"逻辑

请生成{count}个均值回归因子表达式：""",
            
            'volume_price_focused': """生成量价关系导向的技术分析因子表达式。要求：
1. 重点关注价格与成交量的关系
2. 使用ts_corr(), vwap, volume等相关函数
3. 考虑成交量确认价格趋势
4. 结合资金流向分析
5. 表达式应体现"量价配合确认趋势"逻辑

请生成{count}个量价关系因子表达式："""
        }
    
    @classmethod
    def get_data_field_mapping(cls) -> Dict[str, List[str]]:
        """获取数据字段映射"""
        return {
            'price_data': [
                'close', 'open', 'high', 'low', 'vwap'
            ],
            'volume_data': [
                'volume', 'turnover', 'shares_traded'
            ],
            'derived_metrics': [
                'returns', 'log_returns', 'price_change',
                'volume_change', 'high_low_ratio'
            ],
            'technical_indicators': [
                'rsi', 'macd', 'bollinger_bands', 'moving_average',
                'stochastic', 'williams_r'
            ],
            'market_microstructure': [
                'bid', 'ask', 'spread', 'market_impact',
                'order_flow', 'tick_size'
            ]
        }
    
    @classmethod
    def get_common_timeframes(cls) -> Dict[str, List[int]]:
        """获取常用时间框架"""
        return {
            'short_term': [1, 3, 5, 7, 10],
            'medium_term': [15, 20, 30, 40, 60],
            'long_term': [90, 120, 180, 252],
            'intraday': [1, 2, 3, 4, 5],  # for intraday data
            'weekly': [5, 10, 15, 20, 25]  # for weekly analysis
        } 