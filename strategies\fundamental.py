# WQB基本面策略模板
# 脚本功能：提供基本面分析的因子表达式模板和生成逻辑

from typing import List, Dict, Any
from data.schemas import StrategyType

class FundamentalStrategyTemplates:
    """基本面策略模板集合"""
    
    # 基础财务比率模板
    FINANCIAL_RATIOS = [
        "rank(total_assets / total_liabilities)",
        "zscore(revenue / total_assets)",
        "rank(net_income / shareholders_equity)",
        "ts_rank(operating_cash_flow / revenue, 20)",
        "group_neutralize(debt_to_equity_ratio, subindustry)",
        "rank(current_assets / current_liabilities)",
        "zscore(gross_profit_margin)",
        "ts_delta(return_on_assets, 4)",
        "rank(book_value_per_share / close)",
        "group_rank(price_to_earnings_ratio, industry)"
    ]
    
    # 成长性指标模板
    GROWTH_METRICS = [
        "ts_rank(revenue_growth_rate, 12)",
        "rank(earnings_growth_rate)",
        "zscore(ts_mean(revenue_growth_rate, 8))",
        "group_neutralize(asset_growth_rate, sector)",
        "rank(operating_income_growth)",
        "ts_delta(total_assets, 12) / ts_delay(total_assets, 12)",
        "rank(book_value_growth_rate)",
        "zscore(dividend_growth_rate)",
        "ts_rank(cash_flow_growth, 20)",
        "group_rank(earnings_per_share_growth, subindustry)"
    ]
    
    # 估值指标模板
    VALUATION_METRICS = [
        "rank(1 / price_to_book_ratio)",
        "zscore(earnings_yield)",
        "group_neutralize(price_to_sales_ratio, industry)",
        "rank(free_cash_flow_yield)",
        "ts_rank(dividend_yield, 60)",
        "rank(enterprise_value / ebitda)",
        "zscore(price_to_tangible_book)",
        "group_rank(ev_to_sales, sector)",
        "rank(operating_cash_flow / market_cap)",
        "zscore(1 / peg_ratio)"
    ]
    
    # 质量指标模板
    QUALITY_METRICS = [
        "rank(return_on_invested_capital)",
        "zscore(asset_turnover_ratio)",
        "group_neutralize(interest_coverage_ratio, industry)",
        "rank(operating_margin)",
        "ts_rank(free_cash_flow_margin, 20)",
        "zscore(working_capital / total_assets)",
        "rank(tangible_book_value / total_assets)",
        "group_rank(debt_service_coverage, subindustry)",
        "zscore(inventory_turnover)",
        "rank(receivables_turnover)"
    ]
    
    # 复合策略模板
    COMPOSITE_STRATEGIES = [
        "rank(net_income / total_assets) * rank(1 / price_to_book_ratio)",
        "zscore(revenue_growth_rate) + zscore(1 / price_to_earnings_ratio)",
        "group_neutralize(return_on_equity * (1 / price_to_book_ratio), industry)",
        "ts_rank(operating_cash_flow / market_cap, 20) * rank(earnings_growth_rate)",
        "rank(free_cash_flow_yield) + rank(return_on_invested_capital)",
        "zscore(gross_profit_margin) * zscore(asset_turnover_ratio)",
        "group_rank(ebitda_margin, sector) * group_rank(1 / ev_to_ebitda, sector)",
        "ts_delta(return_on_assets, 4) * rank(1 / price_to_tangible_book)",
        "rank(dividend_yield) * rank(dividend_coverage_ratio)",
        "zscore(operating_leverage) * zscore(1 / debt_to_equity_ratio)"
    ]
    
    @classmethod
    def get_all_templates(cls) -> List[str]:
        """获取所有基本面策略模板"""
        all_templates = []
        all_templates.extend(cls.FINANCIAL_RATIOS)
        all_templates.extend(cls.GROWTH_METRICS)
        all_templates.extend(cls.VALUATION_METRICS)
        all_templates.extend(cls.QUALITY_METRICS)
        all_templates.extend(cls.COMPOSITE_STRATEGIES)
        return all_templates
    
    @classmethod
    def get_templates_by_category(cls, category: str) -> List[str]:
        """根据类别获取模板"""
        category_map = {
            'financial_ratios': cls.FINANCIAL_RATIOS,
            'growth': cls.GROWTH_METRICS,
            'valuation': cls.VALUATION_METRICS,
            'quality': cls.QUALITY_METRICS,
            'composite': cls.COMPOSITE_STRATEGIES
        }
        return category_map.get(category.lower(), [])
    
    @classmethod
    def get_ai_generation_prompts(cls) -> Dict[str, str]:
        """获取AI生成提示模板"""
        return {
            'basic_prompt': """基于基本面分析生成量化因子表达式。要求：
1. 使用财务数据字段如revenue, net_income, total_assets, shareholders_equity等
2. 应用适当的变换如rank(), zscore(), group_neutralize()
3. 考虑时间序列特性，使用ts_rank(), ts_delta()等
4. 体现价值投资或成长投资逻辑
5. 确保表达式具有经济学意义

请生成{count}个不同的基本面因子表达式：""",
            
            'value_focused': """生成价值投资导向的基本面因子表达式。要求：
1. 重点关注估值指标如P/E, P/B, EV/EBITDA等的倒数
2. 结合盈利能力指标如ROE, ROA, Operating Margin
3. 考虑财务稳健性如debt ratios, coverage ratios
4. 使用适当的中性化处理避免行业偏见
5. 表达式应体现"买入被低估的高质量公司"逻辑

请生成{count}个价值投资因子表达式：""",
            
            'growth_focused': """生成成长投资导向的基本面因子表达式。要求：
1. 重点关注增长率指标如revenue_growth, earnings_growth
2. 结合运营效率改善如margin expansion, asset turnover improvement  
3. 考虑现金流增长和再投资能力
4. 使用时间序列函数捕捉增长趋势
5. 表达式应体现"买入高速成长公司"逻辑

请生成{count}个成长投资因子表达式：""",
            
            'quality_focused': """生成质量投资导向的基本面因子表达式。要求：
1. 重点关注盈利质量如ROE, ROIC, Free Cash Flow Margin
2. 考虑经营稳定性如earnings volatility, cash conversion
3. 评估财务健康度如debt levels, interest coverage
4. 使用稳定性指标如ts_std_dev(), coefficient of variation
5. 表达式应体现"买入高质量稳定公司"逻辑

请生成{count}个质量投资因子表达式："""
        }
    
    @classmethod
    def get_data_field_mapping(cls) -> Dict[str, List[str]]:
        """获取数据字段映射"""
        return {
            'income_statement': [
                'revenue', 'net_income', 'operating_income', 'gross_profit',
                'ebitda', 'interest_expense', 'tax_expense'
            ],
            'balance_sheet': [
                'total_assets', 'total_liabilities', 'shareholders_equity',
                'current_assets', 'current_liabilities', 'cash_and_equivalents',
                'inventory', 'accounts_receivable', 'property_plant_equipment'
            ],
            'cash_flow': [
                'operating_cash_flow', 'free_cash_flow', 'capital_expenditures',
                'cash_flow_from_operations', 'cash_flow_from_investing'
            ],
            'ratios': [
                'price_to_earnings_ratio', 'price_to_book_ratio', 'debt_to_equity_ratio',
                'current_ratio', 'return_on_equity', 'return_on_assets',
                'asset_turnover_ratio', 'inventory_turnover', 'receivables_turnover'
            ],
            'growth_rates': [
                'revenue_growth_rate', 'earnings_growth_rate', 'book_value_growth_rate',
                'dividend_growth_rate', 'asset_growth_rate'
            ],
            'per_share_metrics': [
                'earnings_per_share', 'book_value_per_share', 'cash_flow_per_share',
                'dividend_per_share'
            ]
        } 