# WQB情绪策略模板
# 脚本功能：提供情绪分析的因子表达式模板和生成逻辑

from typing import List, Dict, Any
from data.schemas import StrategyType

class SentimentStrategyTemplates:
    """情绪策略模板集合"""
    
    # 新闻情绪模板
    NEWS_SENTIMENT = [
        "rank(news_sentiment_score)",
        "zscore(ts_mean(news_sentiment_score, 5))",
        "group_neutralize(news_count, subindustry)",
        "ts_rank(news_positive_ratio, 20)",
        "rank(news_sentiment_change)",
        "zscore(vec_avg(news_sentiment_vec))",
        "group_rank(news_coverage_ratio, industry)",
        "ts_rank(vec_sum(news_buzz_vec), 60)",
        "rank(news_sentiment_score * news_count)",
        "zscore(ts_delta(news_sentiment_score, 1))"
    ]
    
    # 社交媒体情绪模板
    SOCIAL_SENTIMENT = [
        "rank(social_media_sentiment)",
        "zscore(twitter_sentiment_score)",
        "group_neutralize(social_mentions_count, sector)",
        "ts_rank(reddit_sentiment_score, 10)",
        "rank(social_sentiment_volatility)",
        "zscore(vec_avg(social_buzz_vector))",
        "group_rank(social_engagement_ratio, industry)",
        "ts_rank(social_sentiment_trend, 30)",
        "rank(weibo_sentiment_score * weibo_mentions)",
        "zscore(ts_mean(social_sentiment_change, 3))"
    ]
    
    # 市场情绪模板
    MARKET_SENTIMENT = [
        "rank(vix_level)",
        "zscore(put_call_ratio)",
        "group_neutralize(options_sentiment, subindustry)",
        "ts_rank(fear_greed_index, 60)",
        "rank(margin_debt_ratio)",
        "zscore(insider_trading_sentiment)",
        "group_rank(analyst_sentiment_change, industry)",
        "ts_rank(institutional_sentiment, 20)",
        "rank(market_sentiment_index)",
        "zscore(ts_delta(investor_sentiment, 5))"
    ]
    
    # 事件驱动情绪模板
    EVENT_DRIVEN = [
        "trade_when(news_count > 5, news_sentiment_score, -1)",
        "rank(earnings_announcement_sentiment)",
        "zscore(merger_rumor_sentiment)",
        "group_neutralize(regulatory_news_impact, industry)",
        "trade_when(analyst_upgrade, analyst_sentiment_score, 0)",
        "rank(insider_trading_signal * insider_sentiment)",
        "zscore(product_launch_sentiment)",
        "group_rank(management_change_sentiment, sector)",
        "trade_when(earnings_surprise, earnings_sentiment, -1)",
        "rank(partnership_announcement_sentiment)"
    ]
    
    # 相对情绪模板
    RELATIVE_SENTIMENT = [
        "rank(news_sentiment_score / ts_mean(news_sentiment_score, 20))",
        "zscore(news_count / ts_mean(news_count, 60))",
        "group_neutralize(sentiment_rank_industry, industry)",
        "ts_rank(sentiment_momentum, 252)",
        "rank(sentiment_surprise)",
        "zscore(relative_sentiment_strength)",
        "group_rank(sentiment_quality_score, subindustry)",
        "ts_rank(sentiment_consistency, 120)",
        "rank(sentiment_coverage_bias)",
        "zscore(sentiment_flow_ratio)"
    ]
    
    # 复合情绪策略模板
    COMPOSITE_SENTIMENT = [
        "rank(news_sentiment_score) * rank(social_sentiment)",
        "zscore(news_sentiment_score + social_sentiment) / 2",
        "group_neutralize(news_sentiment * news_count, industry)",
        "rank(news_sentiment_score) + rank(analyst_sentiment)",
        "ts_rank(news_sentiment_trend * social_sentiment_trend, 20)",
        "zscore(sentiment_composite_score) * rank(sentiment_confidence)",
        "group_rank(multi_source_sentiment, sector) * group_rank(sentiment_volume, sector)",
        "rank(sentiment_consensus) + rank(sentiment_surprise)",
        "zscore(weighted_sentiment_score) * zscore(sentiment_reliability)",
        "trade_when(sentiment_event_trigger, sentiment_combined_score, 0)"
    ]
    
    @classmethod
    def get_all_templates(cls) -> List[str]:
        """获取所有情绪策略模板"""
        all_templates = []
        all_templates.extend(cls.NEWS_SENTIMENT)
        all_templates.extend(cls.SOCIAL_SENTIMENT)
        all_templates.extend(cls.MARKET_SENTIMENT)
        all_templates.extend(cls.EVENT_DRIVEN)
        all_templates.extend(cls.RELATIVE_SENTIMENT)
        all_templates.extend(cls.COMPOSITE_SENTIMENT)
        return all_templates
    
    @classmethod
    def get_templates_by_category(cls, category: str) -> List[str]:
        """根据类别获取模板"""
        category_map = {
            'news': cls.NEWS_SENTIMENT,
            'social': cls.SOCIAL_SENTIMENT,
            'market': cls.MARKET_SENTIMENT,
            'event': cls.EVENT_DRIVEN,
            'relative': cls.RELATIVE_SENTIMENT,
            'composite': cls.COMPOSITE_SENTIMENT
        }
        return category_map.get(category.lower(), [])
    
    @classmethod
    def get_ai_generation_prompts(cls) -> Dict[str, str]:
        """获取AI生成提示模板"""
        return {
            'basic_prompt': """基于情绪分析生成量化因子表达式。要求：
1. 使用情绪数据如news_sentiment, social_sentiment, analyst_sentiment等
2. 应用适当的变换如rank(), zscore(), group_neutralize()
3. 考虑事件驱动策略，使用trade_when()函数
4. 结合时间序列特性，使用ts_rank(), ts_delta()等
5. 确保表达式具有情绪分析逻辑

请生成{count}个不同的情绪分析因子表达式：""",
            
            'news_focused': """生成新闻情绪导向的因子表达式。要求：
1. 重点关注新闻情绪数据如news_sentiment_score, news_count
2. 使用vec_avg(), vec_sum()处理向量数据
3. 考虑新闻覆盖度和情绪变化
4. 结合事件检测如earnings, mergers等
5. 表达式应体现"新闻驱动投资"逻辑

请生成{count}个新闻情绪因子表达式：""",
            
            'social_focused': """生成社交媒体情绪导向的因子表达式。要求：
1. 重点关注社交媒体数据如twitter_sentiment, reddit_sentiment
2. 考虑社交媒体的高频特性和噪音
3. 结合社交媒体热度和参与度指标
4. 使用适当的平滑和过滤技术
5. 表达式应体现"群体智慧"逻辑

请生成{count}个社交媒体情绪因子表达式：""",
            
            'event_driven_focused': """生成事件驱动情绪策略的因子表达式。要求：
1. 重点关注特定事件如earnings, upgrades, mergers
2. 使用trade_when()函数实现事件触发交易
3. 考虑事件的稀有性和影响持续时间
4. 结合事件前后的情绪变化
5. 表达式应体现"事件套利"逻辑

请生成{count}个事件驱动因子表达式："""
        }
    
    @classmethod
    def get_data_field_mapping(cls) -> Dict[str, List[str]]:
        """获取数据字段映射"""
        return {
            'news_data': [
                'news_sentiment_score', 'news_count', 'news_positive_ratio',
                'news_negative_ratio', 'news_neutral_ratio', 'news_coverage',
                'news_sentiment_change', 'news_buzz_score'
            ],
            'social_media': [
                'twitter_sentiment', 'reddit_sentiment', 'weibo_sentiment',
                'social_mentions', 'social_engagement', 'viral_score',
                'hashtag_sentiment', 'influencer_sentiment'
            ],
            'analyst_data': [
                'analyst_sentiment', 'analyst_upgrades', 'analyst_downgrades',
                'target_price_change', 'recommendation_change',
                'analyst_revision_sentiment'
            ],
            'market_sentiment': [
                'vix_level', 'put_call_ratio', 'fear_greed_index',
                'margin_debt', 'insider_trading', 'options_sentiment',
                'futures_sentiment', 'currency_sentiment'
            ],
            'event_data': [
                'earnings_announcement', 'merger_announcement', 'ipo_sentiment',
                'regulatory_news', 'product_launch', 'management_change',
                'earnings_surprise', 'guidance_change'
            ],
            'vector_data': [
                'news_sentiment_vec', 'social_buzz_vec', 'topic_sentiment_vec',
                'entity_sentiment_vec', 'temporal_sentiment_vec'
            ]
        }
    
    @classmethod
    def get_sentiment_indicators(cls) -> Dict[str, List[str]]:
        """获取情绪指标分类"""
        return {
            'positive_indicators': [
                'bullish_sentiment', 'positive_news_ratio', 'upgrade_sentiment',
                'optimistic_outlook', 'growth_expectations'
            ],
            'negative_indicators': [
                'bearish_sentiment', 'negative_news_ratio', 'downgrade_sentiment',
                'pessimistic_outlook', 'risk_aversion'
            ],
            'volatility_indicators': [
                'sentiment_volatility', 'opinion_divergence', 'uncertainty_index',
                'sentiment_dispersion', 'consensus_breakdown'
            ],
            'momentum_indicators': [
                'sentiment_momentum', 'sentiment_acceleration', 'sentiment_trend',
                'sentiment_persistence', 'sentiment_reversal'
            ]
        }
    
    @classmethod
    def get_event_types(cls) -> Dict[str, List[str]]:
        """获取事件类型分类"""
        return {
            'earnings_events': [
                'earnings_announcement', 'earnings_surprise', 'guidance_update',
                'analyst_day', 'quarterly_results'
            ],
            'corporate_events': [
                'merger_announcement', 'acquisition', 'spinoff', 'dividend_announcement',
                'share_buyback', 'management_change'
            ],
            'regulatory_events': [
                'regulatory_approval', 'legal_settlement', 'policy_change',
                'compliance_issue', 'investigation_news'
            ],
            'market_events': [
                'ipo_announcement', 'index_inclusion', 'sector_rotation',
                'macroeconomic_news', 'geopolitical_events'
            ]
        } 