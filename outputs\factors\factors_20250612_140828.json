{"metadata": {"created_at": "2025-06-12T14:08:28.897555", "factor_count": 9, "version": "1.0"}, "factors": [{"id": "FUN001_3935", "expression": "-zscore(goodwill/sales)*(rank(fn_accrued_liab_a))\r\n", "alpha_id": null, "wqb_alpha_id": "nekbgxM", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.936124", "updated_at": "2025-06-12T14:03:23.936131", "performance": {"fitness": 1.0, "sharpe": 1.16, "turnover": 0.028, "returns": 0.0938, "margin": 0.006703, "max_drawdown": 0.0972, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 10, "neutralization": "INDUSTRY", "truncation": 0.1, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN002_3937", "expression": "reverse(ts_backfill(zscore(winsorize(divide(goodwill,sales))),50))*add(multiply(rank(fn_accrued_liab_a),0.3),multiply(rank(capex),0.25),multiply(rank(divide(dividend,sharesout)),0.25),multiply(rank(debt_st),0.2))*power(rank(cap),0.08)\r\n", "alpha_id": null, "wqb_alpha_id": "Lmlq63L", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.938026", "updated_at": "2025-06-12T14:03:23.938031", "performance": {"fitness": 0.88, "sharpe": 1.09, "turnover": 0.0285, "returns": 0.081, "margin": 0.005692, "max_drawdown": 0.0888, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 10, "neutralization": "INDUSTRY", "truncation": 0.1, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN003_3939", "expression": "-ts_zscore(enterprise_value/ebitda, 63)", "alpha_id": null, "wqb_alpha_id": "ORAOM97", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.940156", "updated_at": "2025-06-12T14:03:23.940161", "performance": {"fitness": 0.77, "sharpe": 1.3, "turnover": 0.2566, "returns": 0.0898, "margin": 0.0007, "max_drawdown": 0.1113, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 0, "neutralization": "INDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN004_3942", "expression": "gross_profit = revenue-cogs;\r\nrd =fnd6_newqv1300_xrdq;\r\nRDM= rd/cap;\r\nRDA= rd/assets; \r\nRDGP= rd/gross_profit;\r\nGPA = gross_profit/assets;\r\n\r\ngroup_neutralize(rank(GPA), bucket(rank(RDGP), range=\"0.1,1,0.1\"))", "alpha_id": null, "wqb_alpha_id": "QN5q9nW", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.942938", "updated_at": "2025-06-12T14:03:23.942943", "performance": {"fitness": 0.83, "sharpe": 1.1, "turnover": 0.0325, "returns": 0.0712, "margin": 0.004376, "max_drawdown": 0.1218, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 4, "neutralization": "SUBINDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN005_3943", "expression": "a=(sga_expense+fnd6_newa2v1300_xrd)/(sga_expense+fnd6_newa2v1300_xrd+assets-goodwill);\r\nb=normalize(anl4_fcfps_flag)*a;\r\ngroup_neutralize(b,bucket(rank(cap),range=\"0.1,1,0.1\"))", "alpha_id": null, "wqb_alpha_id": "x2m9Nzw", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.944273", "updated_at": "2025-06-12T14:03:23.944278", "performance": {"fitness": 0.82, "sharpe": 0.98, "turnover": 0.0403, "returns": 0.0885, "margin": 0.004394, "max_drawdown": 0.1886, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 4, "neutralization": "SECTOR", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN006_3946", "expression": "-ts_zscore(enterprise_value/ebitda, 90)", "alpha_id": null, "wqb_alpha_id": "XV9rg2m", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.947441", "updated_at": "2025-06-12T14:03:23.947449", "performance": {"fitness": 0.63, "sharpe": 1.07, "turnover": 0.213, "returns": 0.0735, "margin": 0.00069, "max_drawdown": 0.1179, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 0, "neutralization": "INDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN007_3949", "expression": "rank(subtract(divide(ebit, assets), ts_mean(divide(ebit, assets), 30)))", "alpha_id": null, "wqb_alpha_id": "r99<PERSON><PERSON><PERSON>", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.949846", "updated_at": "2025-06-12T14:03:23.949854", "performance": {"fitness": 0.66, "sharpe": 1.22, "turnover": 0.1525, "returns": 0.0446, "margin": 0.000584, "max_drawdown": 0.0447, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 0, "neutralization": "INDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN008_3951", "expression": "ts_rank(operating_income,252)", "alpha_id": null, "wqb_alpha_id": "ArOYv7w", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.951504", "updated_at": "2025-06-12T14:03:23.951510", "performance": {"fitness": 0.54, "sharpe": 0.94, "turnover": 0.049, "returns": 0.0419, "margin": 0.001709, "max_drawdown": 0.0973, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 0, "neutralization": "SUBINDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}, {"id": "FUN009_3952", "expression": "zscore(subtract(divide(subtract(assets, debt), assets), ts_mean(divide(subtract(assets, debt), assets), 60)))", "alpha_id": null, "wqb_alpha_id": "gXXRJvJ", "strategy_type": "fundamental", "status": "validated", "created_at": "2025-06-12T14:03:23.952730", "updated_at": "2025-06-12T14:03:23.952737", "performance": {"fitness": 0.54, "sharpe": 0.76, "turnover": 0.0987, "returns": 0.0634, "margin": 0.001284, "max_drawdown": 0.0949, "long_count": null, "short_count": null, "consistency": null}, "quality_checks": {"concentrated_weight": "UNKNOWN", "low_sub_universe_sharpe": "UNKNOWN", "self_correlation": null, "robustness": null}, "simulation_settings": {"instrumentType": "EQUITY", "region": "USA", "universe": "TOP3000", "delay": 1, "decay": 0, "neutralization": "INDUSTRY", "truncation": 0.08, "pasteurization": "ON", "unitHandling": "VERIFY", "nanHandling": "OFF", "language": "FASTEXPR", "visualization": false}, "notes": "", "tags": [], "source_strategy": "", "optimization_history": []}]}