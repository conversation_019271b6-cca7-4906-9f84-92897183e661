# 因子缓存管理器
# 脚本功能：管理因子缓存、增量更新和性能历史追踪

import json
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import logging

from data.schemas import FactorSchema, StrategyType, FactorStatus, PerformanceMetrics

class FactorCacheManager:
    """因子缓存管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 缓存路径配置
        self.cache_dir = Path(config['storage']['base_path']) / 'cache'
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.factors_cache_file = self.cache_dir / 'discovered_factors.json'
        self.performance_history_file = self.cache_dir / 'performance_history.json'
        self.metadata_file = self.cache_dir / 'cache_metadata.json'
        
        # 缓存配置
        self.cache_ttl_hours = config.get('factor_cache', {}).get('ttl_hours', 24)
        self.max_factors_per_strategy = config.get('factor_cache', {}).get('max_factors_per_strategy', 200)
        
        # 加载现有缓存
        self.cached_factors = self._load_cached_factors()
        self.performance_history = self._load_performance_history()
        self.metadata = self._load_metadata()
    
    def _load_cached_factors(self) -> Dict[str, List[FactorSchema]]:
        """加载缓存的因子"""
        try:
            if self.factors_cache_file.exists():
                with open(self.factors_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                cached_factors = {}
                for strategy, factor_list in data.items():
                    cached_factors[strategy] = [
                        FactorSchema.from_dict(factor_dict) 
                        for factor_dict in factor_list
                    ]
                
                self.logger.info(f"加载缓存因子：{sum(len(factors) for factors in cached_factors.values())} 个")
                return cached_factors
            else:
                self.logger.info("未找到因子缓存文件，将创建新缓存")
                return {}
        except Exception as e:
            self.logger.error(f"加载因子缓存失败: {e}")
            return {}
    
    def _load_performance_history(self) -> Dict[str, List[Dict]]:
        """加载性能历史记录"""
        try:
            if self.performance_history_file.exists():
                with open(self.performance_history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"加载性能历史失败: {e}")
            return {}
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载缓存元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                'last_update': None,
                'discovery_count': 0,
                'version': '1.0'
            }
        except Exception as e:
            self.logger.error(f"加载缓存元数据失败: {e}")
            return {'last_update': None, 'discovery_count': 0, 'version': '1.0'}
    
    def is_cache_valid(self) -> bool:
        """检查缓存是否仍然有效"""
        if not self.metadata.get('last_update'):
            return False
        
        last_update = datetime.fromisoformat(self.metadata['last_update'])
        ttl_threshold = datetime.now() - timedelta(hours=self.cache_ttl_hours)
        
        return last_update > ttl_threshold
    
    def get_cached_factors(self, strategy_type: Optional[StrategyType] = None) -> List[FactorSchema]:
        """获取缓存的因子"""
        if strategy_type:
            return self.cached_factors.get(strategy_type.value, [])
        else:
            all_factors = []
            for factors in self.cached_factors.values():
                all_factors.extend(factors)
            return all_factors
    
    def get_cached_factor_ids(self, strategy_type: Optional[StrategyType] = None) -> Set[str]:
        """获取缓存因子的ID集合"""
        factors = self.get_cached_factors(strategy_type)
        return {factor.alpha_id for factor in factors if factor.alpha_id}
    
    def should_discover_new_factors(self, strategy_type: StrategyType, target_count: int) -> bool:
        """判断是否需要发现新因子"""
        cached_count = len(self.get_cached_factors(strategy_type))
        
        # 如果缓存不足目标数量的80%，需要发现新因子
        return cached_count < target_count * 0.8
    
    def add_factors_to_cache(self, factors: List[FactorSchema], strategy_type: StrategyType):
        """添加因子到缓存"""
        strategy_key = strategy_type.value
        
        if strategy_key not in self.cached_factors:
            self.cached_factors[strategy_key] = []
        
        # 过滤重复因子
        existing_ids = self.get_cached_factor_ids(strategy_type)
        new_factors = [
            factor for factor in factors 
            if factor.alpha_id and factor.alpha_id not in existing_ids
        ]
        
        # 添加新因子
        self.cached_factors[strategy_key].extend(new_factors)
        
        # 限制每种策略的最大因子数量
        if len(self.cached_factors[strategy_key]) > self.max_factors_per_strategy:
            # 按性能排序，保留最好的
            self.cached_factors[strategy_key].sort(
                key=lambda f: (f.performance.fitness if f.performance else 0), 
                reverse=True
            )
            self.cached_factors[strategy_key] = self.cached_factors[strategy_key][:self.max_factors_per_strategy]
        
        self.logger.info(f"添加 {len(new_factors)} 个新因子到 {strategy_type.value} 缓存")
    
    def update_factor_performance(self, factor: FactorSchema):
        """更新因子性能历史"""
        if not factor.alpha_id or not factor.performance:
            return
        
        factor_id = factor.alpha_id
        
        if factor_id not in self.performance_history:
            self.performance_history[factor_id] = []
        
        # 添加新的性能记录
        performance_record = {
            'timestamp': datetime.now().isoformat(),
            'fitness': factor.performance.fitness,
            'sharpe': factor.performance.sharpe,
            'turnover': factor.performance.turnover,
            'status': factor.status.value
        }
        
        self.performance_history[factor_id].append(performance_record)
        
        # 限制历史记录数量
        max_history = 50
        if len(self.performance_history[factor_id]) > max_history:
            self.performance_history[factor_id] = self.performance_history[factor_id][-max_history:]
    
    def get_factor_performance_trend(self, factor_id: str) -> Optional[Dict[str, Any]]:
        """获取因子性能趋势"""
        if factor_id not in self.performance_history:
            return None
        
        history = self.performance_history[factor_id]
        if len(history) < 2:
            return None
        
        # 计算趋势
        recent = history[-3:]  # 最近3次记录
        avg_fitness = sum(record['fitness'] for record in recent) / len(recent)
        avg_sharpe = sum(record['sharpe'] for record in recent) / len(recent)
        
        return {
            'avg_fitness': avg_fitness,
            'avg_sharpe': avg_sharpe,
            'record_count': len(history),
            'latest_timestamp': history[-1]['timestamp']
        }
    
    def select_factors_for_testing(self, strategy_type: StrategyType, 
                                 target_count: int) -> List[FactorSchema]:
        """智能选择因子进行测试"""
        cached_factors = self.get_cached_factors(strategy_type)
        
        if len(cached_factors) <= target_count:
            return cached_factors
        
        # 按多个维度排序选择因子
        def factor_score(factor: FactorSchema) -> float:
            score = 0.0
            
            # 基础性能分数
            if factor.performance:
                score += factor.performance.fitness * 0.4
                score += factor.performance.sharpe * 0.3
                score -= factor.performance.turnover * 0.1  # 低换手率更好
            
            # 性能趋势分数
            if factor.alpha_id:
                trend = self.get_factor_performance_trend(factor.alpha_id)
                if trend:
                    score += trend['avg_fitness'] * 0.2
            
            return score
        
        # 排序并选择最优因子
        sorted_factors = sorted(cached_factors, key=factor_score, reverse=True)
        selected = sorted_factors[:target_count]
        
        self.logger.info(f"从 {len(cached_factors)} 个缓存因子中选择了 {len(selected)} 个进行测试")
        return selected
    
    def save_cache(self):
        """保存缓存到文件"""
        try:
            # 保存因子缓存
            cache_data = {}
            for strategy, factors in self.cached_factors.items():
                cache_data[strategy] = [factor.to_dict() for factor in factors]
            
            with open(self.factors_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            
            # 保存性能历史
            with open(self.performance_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.performance_history, f, indent=2, ensure_ascii=False)
            
            # 更新元数据
            self.metadata.update({
                'last_update': datetime.now().isoformat(),
                'discovery_count': self.metadata.get('discovery_count', 0) + 1,
                'total_factors': sum(len(factors) for factors in self.cached_factors.values())
            })
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.info("因子缓存已保存")
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def clear_cache(self, strategy_type: Optional[StrategyType] = None):
        """清理缓存"""
        if strategy_type:
            if strategy_type.value in self.cached_factors:
                del self.cached_factors[strategy_type.value]
                self.logger.info(f"清理了 {strategy_type.value} 策略的缓存")
        else:
            self.cached_factors.clear()
            self.performance_history.clear()
            self.metadata = {'last_update': None, 'discovery_count': 0, 'version': '1.0'}
            self.logger.info("清理了所有缓存")
        
        self.save_cache()
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'total_factors': sum(len(factors) for factors in self.cached_factors.values()),
            'strategies': {},
            'cache_valid': self.is_cache_valid(),
            'last_update': self.metadata.get('last_update'),
            'discovery_count': self.metadata.get('discovery_count', 0)
        }
        
        for strategy, factors in self.cached_factors.items():
            validated_count = sum(1 for f in factors if f.status == FactorStatus.VALIDATED)
            stats['strategies'][strategy] = {
                'total': len(factors),
                'validated': validated_count,
                'avg_fitness': sum(f.performance.fitness for f in factors if f.performance) / len(factors) if factors else 0
            }
        
        return stats 