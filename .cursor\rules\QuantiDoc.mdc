---
description: 
globs: 
alwaysApply: true
---
 “Delay”是Alpha设置中的一个选项，它决定您的策略何时在股票上进行交易。Delay=1表示alpha使用昨天的数据,在早上进行交易; Delay=0表示alpha使用今天的数据，并在当天下午进行交易。

以下是一些创建D0策略的提示：

从D1策略开始，但使用D0数据（如价格-成交量）来生成潜在的信号，例如“开盘价”。除了价格回归的想法，还可以尝试组(group)回归概念-即，可以计算组的平均值，并期望个别股票的价值回归到组的平均值。
接下来，转向新闻(News) D0数据集，您可以使用新闻条目的数量作为事件alpha的触发器。您可以选择罕见事件，例如合并或收购（此数据仅对顾问可用），然后根据该事件分配价值或根据事件发生当天的回报进行分配。您还可以尝试通过创建寻找没有人提到的股票的alpha来打赌“没有新闻就是好消息”。
D0情绪数据集通常源自新闻或社交媒体数据，因此具有相同的特征（流动性，覆盖范围等）。由于情绪分数是派生值，因此通常可以直接使用它们来尝试创建alpha。您还可以在情绪数据字段上测试回归和技术指标，例如相对强度指数（RSI），而不是在价格数据字段上进行测试。
D0 alpha的具体想法通常在与基本面和分析师数据等低周转数据集合作时更有效。
您可以在D0 alpha中使用一些D1数据，例如关系(Relationship)数据集。
D0 alpha通常在流动性较好的股票池中表现良好。
有些alpha在D0和D1中都有效。可能还有其他alpha，其中D0版本优于其D1版本。但是，也可能有特定的D0 alpha在delay 0和delay 1中表现不同。例如，在D0中，股票价格可能会上涨并显示出动量趋势。然而，在D1中，由于被市场抛售，该股票可能开始显示回归趋势。

小贴士2：使用Ts_regression运算符

回归分析是一种在金融领域中用于研究两个或更多变量之间线性关系的统计方法。例如，我们将应用回归分析来研究收盘价和交易加权平均价（VWAP）之间的关系。VWAP是一种衡量股票在一天内交易的平均价格的方法，考虑到交易的价格和交易量。换句话说，VWAP给交易量更高的交易更多的权重，因为它们在市场影响方面更重要。

Alpha Idea

对一只股票的收盘价和VWAP进行回归分析可以提供变量之间关系的重要信息。假设我们认为，收盘价和VWAP之间的正相关关系表明股票需求强劲，因为VWAP随着收盘价的上涨而上升。而负相关关系可能表明该股票需求较弱，因为VWAP随着收盘价上涨而下降。我们可以使用ts_regression运算符来估计收盘价和VWAP之间的beta系数，以5个变量作为输入：(因变量、自变量、时间窗口、滞后和rettype)。

按照上述步骤，一个参考Alpha是：ts_regression(close, vwap, 4, lag = 0, rettype = 2)
收盘价是我们想要预测的因变量，VWAP是自变量，4天是我们要运行回归分析的时间窗口，0表示没有滞后来预测收盘价，最后rettype = 2表示我们希望得到beta系数（β）作为输出。

其他Rettype值

如果我们想要得到其他参数作为输出，可以使用以下“rettype”参数值：

0：误差项
1：y截距（α）
2：beta系数（β）
3：因变量的估计值
4：误差平方和（SSE）
5：总平方和（SST）
6：R平方
7：均方误差（MSE）
8：beta系数（β）的标准差
9：y截距（α）的标准差

小贴士3：使用自定中性化设置提升Alpha表现

什么是中性化？中性化是一种风险降低技术，将资本平均分配在多头和空头头寸上，以中和推动所选组中股票价格的事件效应。有关中性化工作原理的详细说明，请参阅此帖子。

通常的中性化方法：我们不鼓励没有中性化的Alpha，因此您可能已经注意到，使用“MARKET”、“SECTOR”、“INDUSTRY”和“SUBINDUSTRY”等中性化选项可以默认中性化您的Alpha。

自定义中性化：除了这些常见选项外，您还可以使用“group_neutralize()”运算符将自定义组上的α值中性化。

自定义中性化示例：

CUSTOM_GROUP = floor(rank(cap)*4.99);

group_neutralize(alpha, CUSTOM_GROUP)
思路说明：

您首先使工具的“rank(cap)”矢量从0到1的规模。
乘以4.99以将规模从0扩展到4.99。
使用floor()运算符将所有工具分成5个存储桶，从0到4进行缩放。由于我们使用了floor()运算符，现在所有矢量都是整数。现在，我们基于市值制作了分位数组。
该过程背后的原理是，您假设某些风险与公司的市值相关，因此具有相似规模的股票将承担相同的风险。通过在组上进行中性化，我们可以从我们的α值中减轻该风险因素。

双重中性化：在某些情况下，我们可能希望将α值中性化处理多种风险因素。为了实现这一目标，我们可以使用一种称为“双重中性化”的技术。

双重中性化示例：

NEW_GROUP = bucket(rank(rel_num_comp), range=”0.1,1,0.1”);

CUSTOM_GROUP = (INDUSTRY+1)*1000 + NEW_GROUP;

group_neutralize(alpha, densify(group))
思路说明：

首先，我们使用“bucket”运算符使用“rel_num_comp”创建一个新组，该组代表工具的竞争对手数量。创建一个新组，使竞争对手数量落入同一十分位数的工具将被分配相同的值。
接下来，通过将“INDUSTRY”值乘以相当大的常数，然后加上“ NEW_GROUP” ，只有在相同行业且竞争对手数量相似的股票才会具有相似的最终结果并被中性化（在同一行业从事业务且竞争对手数量相似的公司高度可能是竞争对手）。
因此，在这种情况下，我们正在对生产相同产品的公司进行Alpha中性化，这反过来又与该产品相关的风险相同。

值得注意的是，需要使用densify运算符进行更快的计算，有关densify运算符的更多信息可在此帖子中找到。

一些好的办法：尝试使用不同的分组方法，并决定哪些方法最适合您的Alpha。除了众所周知的方法外，以下是一些示例分组方法：

国家(Country)
交易所(Exchange)
关系数据(Relationship Data)
统计组(Statistical groups)
使用Bucket运算符创建自己的组


小贴士4：如何使用Vector运算符和Vector数据

当对Vector数据感到困惑时，使用vec_avg()或vec_choose()运算符使用Vector数据。Vector数据存储了许多有价值的信息，但有时可能很难理解如何使用存储在Vector数据中的值。在这种情况下，vec_avg()运算符非常有用，因为它取出Vector数据中的平均值，可以用作该天Vector数据的代表数据。或者，有时您只需要最新的或最后一个值，之后所有数据都会转换为Matrix格式，并准备好用于创建Alpha。
某些Vector数据可以用作Matrix数据的替代值，假设您想在Alpha中使用无风险利率。您可以找到表示相同内容的Matrix数据“fnd6_newqv1300_optrfrq”,但它的股票覆盖率仅为35％。也有一个表示相同内容的Vector数据“fnd6_newqeventv110_optrfrq”，其覆盖率为85％，因此使用Vector数据会更好。话虽如此，您不能像通常在Matrix数据类型中那样直接使用此字段来获取无风险利率。因此，您可以使用vec_avg(fnd6_newqeventv110_optrfrq)来代替直接使用“fnd6_newqeventv110_optrfrq”获取无风险利率。
使用vec_sum()运算符计算某些数据的当日总和。例如，“scl12_alltype_buzzvec”数据记录一天中存储的股票情绪量。因此，vec_sum运算符可以给出股票每日情绪量的总和，这对于创建Delay-1 Alpha非常有用。
想要选择每天的Vector数据的最后一个值，vec_choose(datafield，nth=-1)是一个很有用的运算符。这对于创建Delay-0 alpha非常有用，因为使用此运算符可以获取数据的最新值，这可以为D0 alpha提供更多有价值的数据。
向量中性化：向量中性化是一种可以在某种程度上中和您的alpha在某些风险因素上的技术，例如动量风险。示例：vector_neut(alpha，ts_ir(returns，240))可以从您的alpha中减少一些动量因素风险，因此最终稍微提高Alpha的夏普比率。对于相同的策略，更好操作可以使用group_vector_neut运算符实现。例如：group_vector_neut(alpha，ts_ir(returns，240)，subindustry)可以在子行业分组水平上，对动量向量进行Alpha中性化。这种操作有时可以更有效。
需要注意的一点是，vector_neut()和group_vector_neut()运算符不仅限于Vector数据。Alpha和用于将Alpha中性化的向量都可以来自Matrix数据类型字段。‘向量’中和是指Alpha向量被另一个向量中和，例如上述示例中的中和向量为ts_ir(returns，240)。

小贴士5：如何提高 Alpha 策略的容量

广义上讲，高容量的 alpha 与三个主要标准有关：高流动性、低相关性和低换手率。其中，对提高容量贡献最大的是低换手率。从定义上看，低换手率意味着 Alpha 的交易频率较低 —— 这意味着处理的交易数量较少，从而我们有能力更轻松地进行交易。降低 Alpha 低效换手的方法有很多。一种非常基础的方法是驼峰操作（ hump operation）。这种操作对现有 Alpha 的 Raw Alpha 值进行分析，然后修改其中的一些值，以降低该 alpha 的换手率。

基础理念：一般来说，对于一个正常的 alpha，其值每天根据其公式变化。很多时候，alpha 的值并没有显著变化，但 alpha 仍需要进行交易。这些交易中产生的 PnL 不大，但涉及的交易成本仍然相当高。这是可以通过简单技巧聪明地减少的浪费换手率。我们可以设定一个阈值，用来表示 Alpha 值变化的百分比，并只在百分比变化超过该阈值时模拟交易。

改进：单一阈值可能会根据市场条件（评估的不同方式，例如，指数的移动/波动性）变动。

每种工具可能有一个可变的阈值（流动性/市值/波动性）。

对于一组（子行业/行业/自定义组）也可以有单一阈值。

提高阈值，不论是一致性还是非一致性，都可以在一定程度上帮助我们，可以先根据一些因素（市值/波动性）对工具进行排名。

潜在的改进方向：波动性的影响对于决定 Alpha 的容量以及驼峰运算中股票的个体阈值而言，比其他任何因素都更重要。换种思路想一下，试着与事件 Alpha 同步思考,即持续监控股票的短期波动性，并保持对平均长期股票波动性的感知。每当股票波动性有大的波动并超过某个（可自定义）阈值时，Alpha 就开始根据其值模拟交易，思考的是这是 Alpha 可能产生模拟利润的时期。对于其他时间，我们保持持有股票，或者我们可以在这些时间内继续进行前面的驼峰操作，但阈值会严格得多。

小贴士6：以下是一些可能帮助你提高Alpha研究的快速提示

通过使用排名、幂运算、尾部等运算符来削减Alpha值的极端值。
若要评估Alpha的稳健性，用rank运算符查看Alpha的表现是否仍然表现良好。这是因为rank运算符将揭示回测的PnL是否仅由少数几只股票生成。
不要试图仅通过调整参数来提高你的Alpha的夏普比率。你的Alpha应该从数学和经济的角度都具有逻辑意义。
不要试图将两个弱信号连接起来以产生一个更强的信号。
为避免过拟合，选择在不同股票池中都适用的参数。
要了解一个数据字段的更新频率，尝试模拟该数据字段本身。如果它产生高换手率，这意味着Alpha的更新更为频繁。
在达到最小夏普和Fitness阈值后，更多地关注回报。夏普和Fitness阈值的存在是为了区分Alpha信号和噪声，因此，当你达到适当的阈值时，更好地集中在模拟回报上，使之成为你的焦点。
大的Decay值可能会对你的Alpha信号的表现产生负面影响。避免仅为了使单个Alpha看起来具有更高的回报而使用Decay。我们建议在你的Alpha中使用合理数量的Decay天数，以避免影响性能。
你应该努力开发具有强大信号、性能一致（跨年度、股票池和地区）并可解释下滑的Alpha。 不要通过添加多个条件或给股票赋予NaN值，从你的Alpha中不必要地过滤掉股票。这可能最终导致“权重集中错误”。你可以阅读有关Winsorization以控制Alpha权重的文章。在模拟设置中设置truncation <=0.1应该适用于大多数情况。 

小贴士9：使用新闻数据集（news datasets）

以下是一个处理新闻数据集的简单策略：

事件检测：对于特定的股票，确定与该股票相关的新闻发生的日子（你可以通过检查NaN和非NaN值来做到这一点）。

分配权重：一旦在新闻信息中发现了相关事件，那么可以将该股票的位置视为新闻对股票价格百分比变化的影响。

持有仓位：对于剩余的日子，你可以简单地持有你已分配的仓位。

根据以上步骤，一个Alpha可能是：trade_when(news_tot_ticks, news_pct_5_min,-1)

改进方向：在这种情况下，所有三个层次都可以进行改进。

事件检测也可以通过其他各种方式进行，不同的检测方法可以显著改变你的Alpha表达式。你可以通过查看Learn Section中的新闻数据字段来想出一些其他的分配权重方式。此外，不仅要持有Alpha，你还可以通过设置阈值或decay Alpha来提高Alpha的性能。欢迎评论！

小贴士10：权重测试的常见问题和建议（Weight Coverage common issues and advice）

权重测试简单地衡量了Alpha中单只股票的资本集中度，并检测过度集中的情况。在BRAIN中，限制为总资本金大小的10%。权重测试对于限制由股票价格波动造成的回撤风险尤其重要，特别是在OS中。

截断：Brain提供了截断来控制Alpha模拟中的股票权重集中度。值0.1意味着切断限制是总资本金大小的10%。然而，这并不能保证Alpha会通过权重测试。该功能的设计目的是以防御的方式保护OS期间的Alpha仓位异常突增，而不是为了通过IS中的权重测试。

要明白，让Alpha通过IS中的权重测试是你需要考虑的。这可以通过理解Alpha的想法、数据和覆盖范围来稳健地完成。

覆盖率：导致未能通过权重测试的主要因素之一是覆盖率。如果回测的任何一点上，多头或空头大小的股票数量少于10，或者总股票数量少于20，那么Alpha无论如何都会未能通过权重测试。通常，覆盖范围低和/或多空计数不平衡的Alpha通常会未能通过权重测试。

Alpha的覆盖范围变化也会增加信号失真和过拟合的机会，特别是在覆盖范围低的时期。

当覆盖范围低（即20只股票）时，Alpha的表现不能维持，也无法与其覆盖范围较高的时期（即500只股票）相比。通常，当Alpha达到其最大覆盖范围的60%时，其表现就是稳定的。

覆盖范围可以使用Brain中的可视化工具来监控

如何处理低覆盖范围：使用可视化来检测覆盖范围的异常变化。注意模拟开始时的任何低覆盖；最好删除覆盖范围低于最终股票计数的60%的开始期间。

尝试：group_count(is_nan (a),market)> 40 ? a:nan. 此运算符检测到由于短期缺失数据导致的计数异常下降。

尝试：Ts_backfill(a, 2)如果数据缺失一天。此运算符检测到由于更新频率不高的数据，如基本数据，导致的低覆盖。

尝试：Ts_backfill(a, 60)对于每季度更新的基本数据。此运算符检测到依赖新闻的想法的覆盖范围的异常变化。

你也可以检测NaN值并使用is_nan()，last_diff_value()，days_from_last_change()进行你自己的回填。

要有创新，你可以通过适当的填充技巧创建新的Alpha，这里列出的还有更多可以探索的。

Alpha量级分布：并非所有的权重测试失败都是由于数据覆盖问题。另一个可能导致失败的主要因素来自过于依赖数据分布的Alpha想法。当数据分布广泛，存在离群值或数据错误时，就会出现权重问题。

通常，权重测试设置有助于总体上处理不频繁的离群值，但是不能保证。另一种方法是通过使用排名（或组排名）函数来改变数据分布。排名旨在平衡多空计数。排名使数据分布看起来像均匀分布。确保你理解并控制数据范围。在处理数据前对数据范围进行正规化是个好习惯。此处也有助于范围正规化函数，如排名，对数，比例和z分数。

其他注意事项： 不要使用大的回溯日数过度使用回填函数，因为这可能会损害性能。 理解使用可视化工具的数据，并使用适当的回溯日数。 排名用作稳健测试（排名测试）的一部分，因此使用排名函数的Alpha更有可能通过排名测试。

最后一条建议：如果你尝试了我们提到的所有方法并且仍然未能通过权重测试，请转向另一个想法。虽然这个想法可能很好，但是以使其通过权重测试的方式表达出来的能力可能不可能，而且总是存在创建能通过权重测试的新Alpha的机会。

小贴士12：辨析rank(-A)与-rank(A)

虽然rank(-A)和-rank(A)回测后产生的绩效指标相同，但它们并非在所有情况下表现都相同。

运算过程

当对这些表达式进行回测时，rank(-returns)将产生范围在[0,1]内的Alpha值，而-rank(returns)将产生范围在[-1,0]内的Alpha值，如下图绿色框所示。



这两个表达式之间的数值差异在后台计算处理过程中将被中性化掉。中性化即我们从组内的每个Alpha值中减去Alpha值的平均值，如红色框所示。中性化后，这两个表达式分配的资本金权重将相同。

尽管这两个Alpha在各自的回测中会产生相同的结果，但当它们与其他表达式或运算符一起使用时，情况并非如此。

与其他Alpha表达式的联用

例如，当这两个Alpha分别与另一个Alpha表达式相乘时，由此产生的Alpha值在范围和间隔上会有所不同，如下图红色框所示。因此，中性化后的值以及最终分配的权重也将有所不同。



与其他运算符的联用

这两个表达式也可以与运算符有不同的交互方式。为了说明这一点，我们有下面的Alpha 1和2，它们使用if_else()运算符。

表达式1：

returns > 0? rank(-returns) : 1

表达式2：

returns > 0? -rank(returns) : 1

考虑到if-else条件，股票6至8的Alpha值将为1，如下图黄色单元格所示。同样，由此产生的表达式值将导致分配的权重不同。



与if_else()条件运算符的交互

我们也可以将这两个子表达式作为if_else()运算符的条件。

表达式1：

rank(-returns) > 0? 0 : 1

表达式2：

-rank(returns) > 0? 0 : 1

在表达式1中，所有股票将被分配权重=1，而在表达式2中，所有股票将被分配权重=0

小贴士14：常见的小困难/我总是差一点点就能提交，有什么Tips吗

通过使用排名、幂、尾等运算符来削减Alpha值的极值点。
为了评估Alpha的稳健性，查看rank Alpha的表现以看是否仍然表现良好。这是因为rank（）会揭示回测的PnL是否仅由少数几只股票生成。
不要试图仅通过调整参数来提高你的Alpha的夏普比率。你的Alpha应该从数学和经济的角度都具有逻辑意义。
不要试图将两个弱信号组合成一个强信号。
为了避免过拟合，选择在不同股票池中都能起作用的参数。 要了解一个数据字段的更新频率，尝试模拟该数据字段本身。如果它产生高换手率，这意味着Alpha的更新更为频繁
在达到最小夏普和适应度阈值后，更多地关注回报。夏普和适应度（Fitness)阈值的存在是为了区分Alpha信号和噪声，因此，当你达到适当的阈值时，最好集中在提高回报上。
大的衰减Decay值可能会对你的Alpha信号的表现产生负面影响。避免仅为了使单个Alpha看起来具有更高的模拟回报而使用衰减Decay。建议在你的Alpha中使用合理数量的衰减Decay，以避免影响性能。
你应该努力开发具有强大信号、稳定性能（跨年份、宇宙和地区）和可解释下滑的Alpha。 不要通过添加多个条件或给股票赋予NaN值，从你的Alpha中不必要地过滤掉股票。这可能最终导致“权重集中错误”。
你可以阅读有关Winsorization以控制Alpha权重的文章。在回测设置中设置截断 <=0.1应该适用于大多数情况。
使用这些技巧([BRAIN TIPS] 6 ways to quickly evaluate a new dataset – WorldQuant BRAIN)查看您Alpha的数值分布，理解您的输出。


