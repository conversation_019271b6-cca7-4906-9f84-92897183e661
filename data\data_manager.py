# WQB数据管理器
# 脚本功能：基于wqb库的智能数据管理和存储系统

import json
import logging
import os
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

import pandas as pd
import yaml
from wqb import WQBSession

from data.schemas import FactorSchema, BatchResult, PerformanceSchema
from utils.helpers import PathManager, ConfigLoader

class DataManager:
    """智能数据管理引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 路径配置
        self.storage_config = config['storage']
        self.base_path = Path(self.storage_config['base_path'])
        self.cache_duration = self.storage_config['cache_duration_hours']
        
        # 创建存储目录
        self._create_storage_directories()
        
        # 数据缓存
        self._factor_cache = {}
        self._performance_cache = {}
        self._last_cache_update = None
    
    def _create_storage_directories(self):
        """创建存储目录结构"""
        directories = [
            self.base_path / 'factors',
            self.base_path / 'results',
            self.base_path / 'cache',
            self.base_path / 'reports',
            self.base_path / 'backups'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"确保目录存在: {directory}")
    
    def save_factors(self, factors: List[FactorSchema], filename: Optional[str] = None) -> str:
        """保存因子到文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"factors_{timestamp}.json"
        
        filepath = self.base_path / 'factors' / filename
        
        # 转换为可序列化格式
        factors_data = [factor.to_dict() for factor in factors]
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'factor_count': len(factors),
                    'version': '1.0'
                },
                'factors': factors_data
            }, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"保存 {len(factors)} 个因子到: {filepath}")
        return str(filepath)
    
    def load_factors(self, filename: str) -> List[FactorSchema]:
        """从文件加载因子"""
        filepath = self.base_path / 'factors' / filename
        
        if not filepath.exists():
            raise FileNotFoundError(f"因子文件不存在: {filepath}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        factors = []
        for factor_data in data.get('factors', []):
            factor = FactorSchema.from_dict(factor_data)
            factors.append(factor)
        
        self.logger.info(f"从 {filepath} 加载 {len(factors)} 个因子")
        return factors
    
    def save_batch_result(self, batch_result: BatchResult, filename: Optional[str] = None) -> str:
        """保存批次结果"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"batch_result_{timestamp}.json"
        
        filepath = self.base_path / 'results' / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(batch_result.to_dict(), f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"保存批次结果到: {filepath}")
        return str(filepath)
    
    def load_batch_result(self, filename: str) -> BatchResult:
        """加载批次结果"""
        filepath = self.base_path / 'results' / filename
        
        if not filepath.exists():
            raise FileNotFoundError(f"结果文件不存在: {filepath}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        batch_result = BatchResult.from_dict(data)
        self.logger.info(f"从 {filepath} 加载批次结果")
        return batch_result
    
    def cache_factor_data(self, factor_id: str, data: Dict[str, Any]):
        """缓存因子数据"""
        cache_key = f"factor_{factor_id}"
        self._factor_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now(),
            'factor_id': factor_id
        }
        
        # 持久化缓存
        cache_file = self.base_path / 'cache' / f"{cache_key}.pkl"
        with open(cache_file, 'wb') as f:
            pickle.dump(self._factor_cache[cache_key], f)
        
        self.logger.debug(f"缓存因子数据: {factor_id}")
    
    def get_cached_factor_data(self, factor_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的因子数据"""
        cache_key = f"factor_{factor_id}"
        
        # 检查内存缓存
        if cache_key in self._factor_cache:
            cached_item = self._factor_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                self.logger.debug(f"从内存缓存获取因子数据: {factor_id}")
                return cached_item['data']
        
        # 检查文件缓存
        cache_file = self.base_path / 'cache' / f"{cache_key}.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    cached_item = pickle.load(f)
                
                if self._is_cache_valid(cached_item['timestamp']):
                    # 重新加载到内存
                    self._factor_cache[cache_key] = cached_item
                    self.logger.debug(f"从文件缓存获取因子数据: {factor_id}")
                    return cached_item['data']
                else:
                    # 删除过期缓存
                    cache_file.unlink()
            except Exception as e:
                self.logger.error(f"读取缓存文件失败: {e}")
        
        return None
    
    def _is_cache_valid(self, timestamp: datetime) -> bool:
        """检查缓存是否有效"""
        expiry_time = timestamp + timedelta(hours=self.cache_duration)
        return datetime.now() < expiry_time
    
    def clean_expired_cache(self):
        """清理过期缓存"""
        cache_dir = self.base_path / 'cache'
        cleaned_count = 0
        
        # 清理内存缓存
        expired_keys = []
        for key, cached_item in self._factor_cache.items():
            if not self._is_cache_valid(cached_item['timestamp']):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._factor_cache[key]
            cleaned_count += 1
        
        # 清理文件缓存
        for cache_file in cache_dir.glob("*.pkl"):
            try:
                with open(cache_file, 'rb') as f:
                    cached_item = pickle.load(f)
                
                if not self._is_cache_valid(cached_item['timestamp']):
                    cache_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                self.logger.error(f"处理缓存文件 {cache_file} 时出错: {e}")
                # 删除损坏的缓存文件
                cache_file.unlink()
                cleaned_count += 1
        
        if cleaned_count > 0:
            self.logger.info(f"清理 {cleaned_count} 个过期缓存项")
    
    def export_to_excel(self, factors: List[FactorSchema], filename: Optional[str] = None) -> str:
        """导出因子到Excel文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"factors_export_{timestamp}.xlsx"
        
        filepath = self.base_path / 'reports' / filename
        
        # 准备数据
        rows = []
        for factor in factors:
            row = {
                'Factor ID': factor.id,
                'Expression': factor.expression,
                'Strategy Type': factor.strategy_type.value,
                'Status': factor.status.value,
                'Fitness': factor.performance.fitness if factor.performance else 0,
                'Sharpe': factor.performance.sharpe if factor.performance else 0,
                'Turnover': factor.performance.turnover if factor.performance else 0,
                'Returns': factor.performance.returns if factor.performance else 0,
                'Created At': factor.created_at.strftime('%Y-%m-%d %H:%M:%S') if factor.created_at else '',
                'Alpha ID': factor.alpha_id or '',
                'Source Strategy': factor.source_strategy or ''
            }
            rows.append(row)
        
        df = pd.DataFrame(rows)
        df.to_excel(filepath, index=False, engine='openpyxl')
        
        self.logger.info(f"导出 {len(factors)} 个因子到Excel: {filepath}")
        return str(filepath)
    
    def backup_data(self, backup_name: Optional[str] = None) -> str:
        """备份所有数据"""
        if not backup_name:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{timestamp}"
        
        backup_dir = self.base_path / 'backups' / backup_name
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份因子文件
        factors_backup = backup_dir / 'factors'
        factors_backup.mkdir(exist_ok=True)
        factors_source = self.base_path / 'factors'
        if factors_source.exists():
            for file in factors_source.glob('*.json'):
                (factors_backup / file.name).write_text(file.read_text(encoding='utf-8'), encoding='utf-8')
        
        # 备份结果文件
        results_backup = backup_dir / 'results'
        results_backup.mkdir(exist_ok=True)
        results_source = self.base_path / 'results'
        if results_source.exists():
            for file in results_source.glob('*.json'):
                (results_backup / file.name).write_text(file.read_text(encoding='utf-8'), encoding='utf-8')
        
        # 创建备份元数据
        metadata = {
            'backup_name': backup_name,
            'created_at': datetime.now().isoformat(),
            'factors_count': len(list(factors_source.glob('*.json'))) if factors_source.exists() else 0,
            'results_count': len(list(results_source.glob('*.json'))) if results_source.exists() else 0
        }
        
        metadata_file = backup_dir / 'metadata.json'
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据备份完成: {backup_dir}")
        return str(backup_dir)
    
    def restore_from_backup(self, backup_name: str):
        """从备份恢复数据"""
        backup_dir = self.base_path / 'backups' / backup_name
        
        if not backup_dir.exists():
            raise FileNotFoundError(f"备份不存在: {backup_dir}")
        
        # 恢复因子文件
        factors_backup = backup_dir / 'factors'
        if factors_backup.exists():
            factors_dest = self.base_path / 'factors'
            for file in factors_backup.glob('*.json'):
                (factors_dest / file.name).write_text(file.read_text(encoding='utf-8'), encoding='utf-8')
        
        # 恢复结果文件
        results_backup = backup_dir / 'results'
        if results_backup.exists():
            results_dest = self.base_path / 'results'
            for file in results_backup.glob('*.json'):
                (results_dest / file.name).write_text(file.read_text(encoding='utf-8'), encoding='utf-8')
        
        self.logger.info(f"从备份恢复数据: {backup_name}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            'factors': {
                'count': len(list((self.base_path / 'factors').glob('*.json'))),
                'size_mb': 0
            },
            'results': {
                'count': len(list((self.base_path / 'results').glob('*.json'))),
                'size_mb': 0
            },
            'cache': {
                'count': len(list((self.base_path / 'cache').glob('*.pkl'))),
                'size_mb': 0
            },
            'backups': {
                'count': len(list((self.base_path / 'backups').glob('*'))),
                'size_mb': 0
            }
        }
        
        # 计算大小
        for category in stats:
            total_size = 0
            category_path = self.base_path / category
            if category_path.exists():
                for file in category_path.rglob('*'):
                    if file.is_file():
                        total_size += file.stat().st_size
            stats[category]['size_mb'] = round(total_size / 1024 / 1024, 2)
        
        return stats
    
    def list_available_files(self, category: str = 'factors') -> List[Dict[str, Any]]:
        """列出可用文件"""
        category_path = self.base_path / category
        
        if not category_path.exists():
            return []
        
        files_info = []
        for file in category_path.glob('*.json'):
            stat = file.stat()
            files_info.append({
                'filename': file.name,
                'size_mb': round(stat.st_size / 1024 / 1024, 2),
                'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'full_path': str(file)
            })
        
        # 按修改时间排序
        files_info.sort(key=lambda x: x['modified_at'], reverse=True)
        return files_info