# WQB数据模式定义
# 脚本功能：定义因子数据结构、性能指标模式和验证规则

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
import json

class FactorStatus(Enum):
    """因子状态枚举"""
    GENERATED = "generated"              # 已生成/发现
    SIMULATED = "simulated"              # 已模拟
    SIMULATION_FAILED = "simulation_failed"  # 模拟失败
    VALIDATED = "validated"              # 验证通过（达到质量标准）
    OPTIMIZING = "optimizing"            # 优化中
    READY = "ready"                      # 准备提交
    SUBMITTED = "submitted"              # 已提交
    ACCEPTED = "accepted"                # 已接受
    REJECTED = "rejected"                # 已拒绝
    FAILED = "failed"                    # 测试失败

class StrategyType(Enum):
    """策略类型枚举"""
    FUNDAMENTAL = "fundamental"
    TECHNICAL = "technical"
    SENTIMENT = "sentiment"
    MIXED = "mixed"

@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    fitness: float = 0.0
    sharpe: float = 0.0
    turnover: float = 0.0
    returns: float = 0.0
    margin: Optional[float] = None
    max_drawdown: Optional[float] = None
    long_count: Optional[int] = None
    short_count: Optional[int] = None
    consistency: Optional[float] = None
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'fitness': self.fitness,
            'sharpe': self.sharpe,
            'turnover': self.turnover,
            'returns': self.returns,
            'margin': self.margin,
            'max_drawdown': self.max_drawdown,
            'long_count': self.long_count,
            'short_count': self.short_count,
            'consistency': self.consistency
        }
    
    def meets_criteria(self, criteria: Dict[str, float]) -> bool:
        """检查是否满足质量标准"""
        return (
            self.fitness >= criteria.get('min_fitness', 1.0) and
            self.sharpe >= criteria.get('min_sharpe', 1.25) and
            self.turnover <= criteria.get('max_turnover', 0.7) and
            self.turnover >= criteria.get('min_turnover', 0.01)
        )

@dataclass 
class QualityChecks:
    """质量检查结果"""
    concentrated_weight: str = "UNKNOWN"
    low_sub_universe_sharpe: str = "UNKNOWN"
    self_correlation: Optional[str] = None
    robustness: Optional[str] = None
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            'concentrated_weight': self.concentrated_weight,
            'low_sub_universe_sharpe': self.low_sub_universe_sharpe,
            'self_correlation': self.self_correlation,
            'robustness': self.robustness
        }
    
    def all_passed(self, required_checks: List[str]) -> bool:
        """检查所有必需的检查是否通过"""
        check_map = {
            'CONCENTRATED_WEIGHT': self.concentrated_weight,
            'LOW_SUB_UNIVERSE_SHARPE': self.low_sub_universe_sharpe,
            'SELF_CORRELATION': self.self_correlation,
            'ROBUSTNESS': self.robustness
        }
        
        for check_name in required_checks:
            if check_map.get(check_name) != "PASS":
                return False
        return True

@dataclass
class SimulationSettings:
    """模拟设置"""
    instrumentType: str = "EQUITY"
    region: str = "USA"
    universe: str = "TOP3000"
    delay: int = 1
    decay: int = 0
    neutralization: str = "INDUSTRY"
    truncation: float = 0.08
    pasteurization: str = "ON"
    unitHandling: str = "VERIFY"
    nanHandling: str = "OFF"
    language: str = "FASTEXPR"
    visualization: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'instrumentType': self.instrumentType,
            'region': self.region,
            'universe': self.universe,
            'delay': self.delay,
            'decay': self.decay,
            'neutralization': self.neutralization,
            'truncation': self.truncation,
            'pasteurization': self.pasteurization,
            'unitHandling': self.unitHandling,
            'nanHandling': self.nanHandling,
            'language': self.language,
            'visualization': self.visualization
        }
    
    @classmethod
    def from_wqb_data(cls, alpha_data: Dict[str, Any], logger=None) -> 'SimulationSettings':
        """从WQB alpha数据中提取原始模拟设置"""
        import logging
        if logger is None:
            logger = logging.getLogger(__name__)
        
        # 尝试从多个可能的位置提取settings
        settings = None
        extract_source = "默认值"
        
        # 优先从顶层settings字段获取
        if 'settings' in alpha_data and isinstance(alpha_data['settings'], dict):
            settings = alpha_data['settings']
            extract_source = "alpha_data['settings']"
            logger.debug("从alpha_data['settings']获取设置")
        # 备选：从is字段中的settings获取
        elif 'is' in alpha_data and isinstance(alpha_data['is'], dict):
            is_data = alpha_data['is']
            if 'settings' in is_data:
                settings = is_data['settings']
                extract_source = "alpha_data['is']['settings']"
                logger.debug("从alpha_data['is']['settings']获取设置")
            # 新增：直接从is字段提取设置信息
            elif any(key in is_data for key in ['instrumentType', 'region', 'universe', 'delay', 'decay', 'neutralization']):
                settings = is_data
                extract_source = "alpha_data['is']"
                logger.debug("从alpha_data['is']直接获取设置")
        
        if settings:
            try:
                # 提取设置值，检查多种可能的字段名称
                neutralization = (settings.get('neutralization') or 
                                settings.get('neutralisation') or 
                                settings.get('neutralize') or 
                                'INDUSTRY')
                
                # 创建SimulationSettings对象，使用WQB原始设置
                result = cls(
                    instrumentType=settings.get('instrumentType', 'EQUITY'),
                    region=settings.get('region', 'USA'),
                    universe=settings.get('universe', 'TOP3000'),
                    delay=int(settings.get('delay', 1)),
                    decay=int(settings.get('decay', 0)),
                    neutralization=neutralization,
                    truncation=float(settings.get('truncation', 0.08)),
                    pasteurization=settings.get('pasteurization', 'ON'),
                    unitHandling=settings.get('unitHandling', 'VERIFY'),
                    nanHandling=settings.get('nanHandling', 'OFF'),
                    language=settings.get('language', 'FASTEXPR'),
                    visualization=bool(settings.get('visualization', False))
                )
                
                logger.debug(f"从{extract_source}提取设置成功: neutralization={neutralization}")
                return result
            except (ValueError, TypeError) as e:
                logger.warning(f"WQB设置数据解析错误: {e}，使用默认设置")
                return cls()
        else:
            logger.warning("WQB alpha数据中未找到settings字段，使用默认设置")
            return cls()

@dataclass
class FactorSchema:
    """因子数据架构"""
    id: str = ""
    expression: str = ""
    alpha_id: Optional[str] = None
    wqb_alpha_id: Optional[str] = None  # WQB平台上的alpha ID
    strategy_type: StrategyType = StrategyType.MIXED
    status: FactorStatus = FactorStatus.GENERATED
    
    # 时间信息
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # 性能指标
    performance: PerformanceMetrics = field(default_factory=PerformanceMetrics)
    quality_checks: QualityChecks = field(default_factory=QualityChecks)
    simulation_settings: SimulationSettings = field(default_factory=SimulationSettings)
    
    # 元数据
    notes: str = ""
    tags: List[str] = field(default_factory=list)
    source_strategy: str = ""
    optimization_history: List[Dict] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'expression': self.expression,
            'alpha_id': self.alpha_id,
            'wqb_alpha_id': self.wqb_alpha_id,
            'strategy_type': self.strategy_type.value,
            'status': self.status.value,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'performance': self.performance.to_dict(),
            'quality_checks': self.quality_checks.to_dict(),
            'simulation_settings': self.simulation_settings.to_dict(),
            'notes': self.notes,
            'tags': self.tags,
            'source_strategy': self.source_strategy,
            'optimization_history': self.optimization_history
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FactorSchema':
        """从字典创建因子对象"""
        # 处理性能指标
        perf_data = data.get('performance', {})
        # 兼容性处理：如果有旧的drawdown字段，转换为max_drawdown
        if 'drawdown' in perf_data and 'max_drawdown' not in perf_data:
            perf_data['max_drawdown'] = perf_data.pop('drawdown')
        performance = PerformanceMetrics(**perf_data)
        
        # 处理质量检查
        quality_data = data.get('quality_checks', {})
        quality_checks = QualityChecks(**quality_data)
        
        # 处理模拟设置
        sim_data = data.get('simulation_settings', {})
        simulation_settings = SimulationSettings(**sim_data)
        
        # 处理枚举类型
        strategy_type = StrategyType(data.get('strategy_type', 'mixed'))
        status = FactorStatus(data.get('status', 'generated'))
        
        return cls(
            id=data.get('id', ''),
            expression=data.get('expression', ''),
            alpha_id=data.get('alpha_id'),
            wqb_alpha_id=data.get('wqb_alpha_id'),
            strategy_type=strategy_type,
            status=status,
            created_at=data.get('created_at', datetime.now().isoformat()),
            updated_at=data.get('updated_at', datetime.now().isoformat()),
            performance=performance,
            quality_checks=quality_checks,
            simulation_settings=simulation_settings,
            notes=data.get('notes', ''),
            tags=data.get('tags', []),
            source_strategy=data.get('source_strategy', ''),
            optimization_history=data.get('optimization_history', [])
        )
    
    def update_status(self, new_status: FactorStatus):
        """更新状态并记录时间"""
        self.status = new_status
        self.updated_at = datetime.now().isoformat()
    
    def add_optimization_record(self, original_expr: str, new_expr: str, 
                              old_performance: PerformanceMetrics, 
                              new_performance: PerformanceMetrics):
        """添加优化记录"""
        record = {
            'timestamp': datetime.now().isoformat(),
            'original_expression': original_expr,
            'optimized_expression': new_expr,
            'performance_improvement': {
                'fitness_delta': new_performance.fitness - old_performance.fitness,
                'sharpe_delta': new_performance.sharpe - old_performance.sharpe,
                'turnover_delta': new_performance.turnover - old_performance.turnover
            }
        }
        self.optimization_history.append(record)
        self.updated_at = datetime.now().isoformat()

@dataclass
class BatchResult:
    """批量处理结果"""
    total_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    results: List[FactorSchema] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    def add_success(self, factor: FactorSchema):
        """添加成功结果"""
        self.results.append(factor)
        self.success_count += 1
        self.total_count += 1
    
    def add_failure(self, error: str):
        """添加失败结果"""
        self.errors.append(error)
        self.failed_count += 1
        self.total_count += 1
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_count == 0:
            return 0.0
        return self.success_count / self.total_count

class PerformanceSchema:
    """性能数据架构"""
    
    @staticmethod
    def validate_performance_data(data: Dict[str, Any]) -> bool:
        """验证性能数据的完整性"""
        required_fields = ['fitness', 'sharpe', 'turnover', 'returns']
        
        for field in required_fields:
            if field not in data:
                return False
            if not isinstance(data[field], (int, float)):
                return False
        
        return True
    
    @staticmethod
    def extract_from_wqb_response(response_data: Dict[str, Any]) -> PerformanceMetrics:
        """从WQB API响应中提取性能指标"""
        is_data = response_data.get('is', {})
        
        return PerformanceMetrics(
            fitness=float(is_data.get('fitness', 0.0)),
            sharpe=float(is_data.get('sharpe', 0.0)),
            turnover=float(is_data.get('turnover', 0.0)),
            returns=float(is_data.get('returns', 0.0)),
            margin=is_data.get('margin'),
            drawdown=is_data.get('drawdown'),
            consistency=is_data.get('consistency')
        )
    
    @staticmethod
    def extract_quality_checks(response_data: Dict[str, Any]) -> QualityChecks:
        """从WQB API响应中提取质量检查结果"""
        is_data = response_data.get('is', {})
        checks = {check['name']: check['result'] for check in is_data.get('checks', [])}
        
        return QualityChecks(
            concentrated_weight=checks.get('CONCENTRATED_WEIGHT', 'UNKNOWN'),
            low_sub_universe_sharpe=checks.get('LOW_SUB_UNIVERSE_SHARPE', 'UNKNOWN'),
            self_correlation=checks.get('SELF_CORRELATION'),
            robustness=checks.get('ROBUSTNESS')
        ) 