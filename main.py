#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WQB因子挖掘系统 - 完全按照WQB库标准实现
无任何多余封装，严格遵循WQB文档指示
"""

import asyncio
import argparse
import logging
import yaml
from pathlib import Path
from datetime import datetime
from typing import List

import wqb
from wqb import WQBSession

from data.schemas import FactorSchema, StrategyType, FactorStatus
from data.data_manager import DataManager

from engines.factor_generator import FactorGenerator
from engines.quality_controller import QualityController


class WQBFactorMiningSystem:
    """WQB因子挖掘系统 - 简化版，按照WQB标准"""
    
    def __init__(self):
        self.config = None
        self.logger = self._setup_logging()
        
        # 组件
        self.wqb_session = None
        self.factor_generator = None
        self.data_manager = None
        self.quality_controller = None
    
    def _setup_logging(self) -> logging.Logger:
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        log_filename = f"wqb{timestamp}.log"
        logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
        return logging.getLogger(log_filename)
    
    def initialize(self):
        """系统初始化"""
        self.logger.info("初始化WQB因子挖掘系统...")
        
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 初始化WQB会话 - 按WQB文档标准
        self._init_wqb_session()
        
        # 初始化其他组件
        self.factor_generator = FactorGenerator(self.config, self.wqb_session)
        self.data_manager = DataManager(self.config)
        self.quality_controller = QualityController(self.config)
        
        self.logger.info("系统初始化完成")
    
    def _init_wqb_session(self):
        """初始化WQB会话 - 按文档标准，无手动认证"""
        credentials = self._load_credentials()
        
        # 按WQB文档：直接创建，无需手动认证
        self.wqb_session = WQBSession((credentials[0], credentials[1]), logger=self.logger)
        self.logger.info("WQB会话创建完成")
    
    def _load_credentials(self) -> tuple:
        """加载WQB凭据"""
        wqb_config = self.config['wqb']
        credentials_file = wqb_config.get('credentials_file', 'WQB/credential.txt')
        
        try:
            # 读取凭据文件
            credentials_path = Path(credentials_file)
            if not credentials_path.exists():
                raise FileNotFoundError(f"凭据文件不存在: {credentials_file}")
            
            with open(credentials_path, 'r', encoding='utf-8') as f:
                import json
                credentials = json.load(f)
            
            # 解析JSON数组格式 ["email", "token"]
            if isinstance(credentials, list) and len(credentials) >= 2:
                email, token = credentials[0], credentials[1]
                return (email, token)
            else:
                raise ValueError("凭据文件格式错误，期望JSON数组: [\"email\", \"token\"]")
                
        except Exception as e:
            self.logger.error(f"加载凭据失败: {e}")
            raise
    
    async def run_workflow(self):
        """运行工作流 - 按WQB文档标准实现"""
        self.logger.info("开始因子挖掘工作流...")
        
        try:
            # 第一阶段：获取因子
            factors = self._get_factors()
            
            # 第二阶段：WQB模拟 - 按WQB 0.2.5文档标准
            simulated_factors = await self._simulate_factors(factors)
            
            # 第二阶段.5：验证所有模拟记录在WQB平台上的存在性
            verified_factors = await self._verify_all_simulations_on_platform(simulated_factors)
            
            # 第三阶段：评估和保存
            final_factors = self._evaluate_and_save(verified_factors)
            
            self.logger.info(f"工作流完成，最终因子数量: {len(final_factors)}")
            return final_factors
            
        except Exception as e:
            self.logger.error(f"工作流失败: {e}")
            raise
    
    def _get_factors(self) -> List[FactorSchema]:
        """从WQB实时获取因子 - 无缓存版本"""
        self.logger.info("开始从WQB实时获取因子...")
        all_factors = []
        
        for strategy_type in [StrategyType.FUNDAMENTAL, StrategyType.TECHNICAL, StrategyType.SENTIMENT]:
            strategy_config = self.config['factor_generation']['strategies'][strategy_type.value]
            target_count = strategy_config['max_factors']
            
            self.logger.info(f"获取 {strategy_type.value} 策略因子，目标数量: {target_count}")
            
            # 从WQB实时获取因子，无缓存
            wqb_factors = self.factor_generator.generate_factors(strategy_type, target_count)
            all_factors.extend(wqb_factors)
            
            # 记录详细信息用于调试
            if wqb_factors:
                self.logger.info(f"{strategy_type.value}: 成功获取 {len(wqb_factors)} 个因子")
                for i, factor in enumerate(wqb_factors[:3]):  # 只记录前3个的详情
                    self.logger.info(f"  因子{i+1}: {factor.id} - {factor.expression[:30]}...")
            else:
                self.logger.warning(f"{strategy_type.value}: 未获取到任何因子")
        
        self.logger.info(f"总计获取 {len(all_factors)} 个新因子")
        
        # 验证因子唯一性
        factor_ids = [f.id for f in all_factors]
        unique_ids = set(factor_ids)
        if len(factor_ids) != len(unique_ids):
            self.logger.warning(f"检测到重复的因子ID: {len(factor_ids)} 总数 vs {len(unique_ids)} 唯一")
        else:
            self.logger.info(f"所有因子ID都是唯一的")
        
        return all_factors
    async def _simulate_factors(self, factors: List[FactorSchema]) -> List[FactorSchema]:
        """WQB标准模拟 - 使用标准simulate + check模式"""
        self.logger.info(f"开始WQB标准模拟，因子数量: {len(factors)}")
        
        # 获取配置参数
        batch_size = self.config.get('simulation', {}).get('batch_size', 3)
        batch_delay = self.config.get('simulation', {}).get('batch_delay', 10.0)
        max_retries = self.config.get('simulation', {}).get('max_retries', 3)
        
        # 为每个因子准备模拟数据
        for factor in factors:
            # 清除原有的WQB alpha ID，确保创建新的模拟
            factor.wqb_alpha_id = None
            self.logger.info(f"因子 {factor.id} 准备模拟: 表达式={factor.expression[:50]}...")
        
        simulated_factors = []
        
        # 分批处理，避免一次提交过多
        for batch_start in range(0, len(factors), batch_size):
            batch_end = min(batch_start + batch_size, len(factors))
            batch_factors = factors[batch_start:batch_end]
            
            self.logger.info(f"批量提交第 {batch_start//batch_size + 1} 批，因子 {batch_start+1}-{batch_end}")
            
            # 使用标准simulate方法处理批次
            batch_completed = await self._simulate_batch_standard(
                batch_factors, max_retries
            )
            simulated_factors.extend(batch_completed)
            
            # 批次间延迟，避免API速率限制
            if batch_start + batch_size < len(factors):  # 不是最后一批
                self.logger.info(f"批次间延迟 {batch_delay} 秒...")
                await asyncio.sleep(batch_delay)
        
        self.logger.info(f"标准模拟完成，成功: {len(simulated_factors)}, 失败: {len(factors) - len(simulated_factors)}")
        return simulated_factors
    
    async def _simulate_batch_standard(self, batch_factors: List[FactorSchema], max_retries: int) -> List[FactorSchema]:
        """使用WQB标准concurrent_simulate方法批量提交Simulation"""
        import asyncio
        
        # 准备批量模拟的alpha对象
        alphas = []
        factor_map = {}  # 用于映射alpha索引到factor的关系
        
        for i, factor in enumerate(batch_factors):
            alpha = {
                'type': 'REGULAR',
                'settings': factor.simulation_settings.to_dict(),
                'regular': factor.expression,
            }
            alphas.append(alpha)
            factor_map[i] = factor
            self.logger.info(f"准备批量提交Simulation {factor.id}: {factor.expression[:50]}...")
        
        # 使用WQB标准concurrent_simulate方法
        concurrency = self.config.get('simulation', {}).get('concurrency', 3)
        
        try:
            self.logger.info(f"使用concurrent_simulate批量提交 {len(alphas)} 个Simulation，并发度: {concurrency}")
            
            # 调用WQB标准批量Simulation提交API
            responses = await self.wqb_session.concurrent_simulate(
                targets=alphas,
                concurrency=concurrency,
                return_exceptions=True,
                log=f"批量提交 {len(alphas)} 个Simulation",
                log_gap=1
            )
            
            # 处理批量响应
            batch_alphas = []
            for i, response in enumerate(responses):
                factor = factor_map[i]
                
                if isinstance(response, Exception):
                    self.logger.error(f"因子 {factor.id} Simulation提交异常: {response}")
                    factor.status = FactorStatus.SIMULATION_FAILED
                    continue
                
                if response and response.ok:
                    try:
                        result = response.json()
                        # WQB concurrent_simulate返回的是完成的simulation结果
                        simulation_status = result.get('status', '').lower()
                        
                        if simulation_status == 'complete':
                            # WQB simulation成功完成，提取alpha信息
                            # 正确的字段优先级：alpha > alpha_id > alphaId > id
                            alpha_id = (result.get('alpha') or 
                                      result.get('alpha_id') or 
                                      result.get('alphaId') or 
                                      result.get('id'))
                            
                            # 记录字段提取过程用于调试
                            available_fields = {k: v for k, v in result.items() if k in ['alpha', 'alpha_id', 'alphaId', 'id']}
                            self.logger.info(f"因子 {factor.id} 可用ID字段: {available_fields}")
                            
                            if alpha_id:
                                factor.wqb_alpha_id = alpha_id
                                factor.status = FactorStatus.SIMULATED
                                batch_alphas.append((alpha_id, factor))
                                self.logger.info(f"因子 {factor.id} Simulation成功完成，Alpha ID: {alpha_id}")
                            else:
                                # 如果没有直接的alpha_id，尝试从响应URL提取
                                simulation_url = str(response.url)
                                if 'simulations/' in simulation_url:
                                    # 从URL中提取simulation ID作为alpha_id
                                    sim_id = simulation_url.split('simulations/')[-1].split('?')[0]
                                    factor.wqb_alpha_id = sim_id
                                    factor.status = FactorStatus.SIMULATED
                                    batch_alphas.append((sim_id, factor))
                                    self.logger.info(f"因子 {factor.id} Simulation完成，从URL提取ID: {sim_id}")
                                else:
                                    self.logger.error(f"因子 {factor.id} Simulation完成但无法提取alpha_id，响应: {result}")
                                    factor.status = FactorStatus.SIMULATION_FAILED
                        elif simulation_status in ['running', 'pending', 'queued']:
                            # Simulation仍在运行，需要等待（这种情况在concurrent_simulate中应该很少见）
                            simulation_url = response.url
                            batch_alphas.append((simulation_url, factor))
                            self.logger.info(f"因子 {factor.id} Simulation仍在运行: {simulation_url}")
                        elif simulation_status in ['failed', 'error']:
                            self.logger.error(f"因子 {factor.id} Simulation失败: {simulation_status}")
                            factor.status = FactorStatus.SIMULATION_FAILED
                        else:
                            # 对于未知状态，记录详细信息但不立即标记为失败
                            self.logger.warning(f"因子 {factor.id} Simulation状态未知: {simulation_status}，响应: {result}")
                            # 尝试提取alpha_id，可能是成功但状态字段不标准
                            # 正确的字段优先级：alpha > alpha_id > alphaId > id
                            alpha_id = (result.get('alpha') or 
                                      result.get('alpha_id') or 
                                      result.get('alphaId') or 
                                      result.get('id'))
                            
                            # 记录字段提取过程用于调试
                            available_fields = {k: v for k, v in result.items() if k in ['alpha', 'alpha_id', 'alphaId', 'id']}
                            self.logger.info(f"因子 {factor.id} 未知状态可用ID字段: {available_fields}")
                            
                            if alpha_id:
                                factor.wqb_alpha_id = alpha_id
                                factor.status = FactorStatus.SIMULATED
                                batch_alphas.append((alpha_id, factor))
                                self.logger.info(f"因子 {factor.id} 从未知状态响应中提取Alpha ID: {alpha_id}")
                            else:
                                factor.status = FactorStatus.SIMULATION_FAILED
                    except Exception as e:
                        self.logger.error(f"因子 {factor.id} 响应解析失败: {e}")
                        factor.status = FactorStatus.SIMULATION_FAILED
                else:
                    error_msg = response.text if response else "无响应"
                    self.logger.error(f"因子 {factor.id} Simulation提交失败: {error_msg[:100]}")
                    factor.status = FactorStatus.SIMULATION_FAILED
            
            # 处理simulation结果
            if batch_alphas:
                self.logger.info(f"批量提交成功 {len(batch_alphas)} 个Simulation，开始处理...")
                completed_factors = await self._process_simulations(batch_alphas)
                return completed_factors
            else:
                self.logger.warning("没有成功提交的Simulation")
                return []
                
        except Exception as e:
            self.logger.error(f"批量Simulation提交失败: {e}")
            # 标记所有因子为失败
            for factor in batch_factors:
                factor.status = FactorStatus.SIMULATION_FAILED
            return []


    
    async def _verify_alpha_on_platform(self, alpha_id: str, factor_id: str) -> bool:
        """验证alpha在WQB平台上的存在性"""
        try:
            # 使用locate_alpha API验证alpha在平台上的存在
            response = self.wqb_session.locate_alpha(alpha_id)
            
            if response.ok:
                alpha_data = response.json()
                self.logger.info(f"因子 {factor_id} 的alpha {alpha_id} 已在WQB平台验证存在")
                
                # 验证关键字段确保数据完整性
                required_fields = ['id', 'regular']
                missing_fields = [field for field in required_fields if field not in alpha_data]
                
                if missing_fields:
                    self.logger.warning(f"Alpha {alpha_id} 数据不完整，缺少字段: {missing_fields}")
                    return False
                
                # 验证alpha ID匹配
                if alpha_data.get('id') != alpha_id:
                    self.logger.error(f"Alpha ID不匹配: 期望 {alpha_id}, 实际 {alpha_data.get('id')}")
                    return False
                
                self.logger.info(f"Alpha {alpha_id} 平台验证通过，数据完整")
                return True
            else:
                self.logger.error(f"验证alpha {alpha_id} 失败: HTTP {response.status_code}")
                if response.status_code == 404:
                    self.logger.error(f"Alpha {alpha_id} 在WQB平台上不存在")
                elif response.status_code == 403:
                    self.logger.error(f"无权限访问alpha {alpha_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"验证alpha {alpha_id} 时发生异常: {e}")
            return False

    async def _process_simulations(self, batch_items: List[tuple]) -> List[FactorSchema]:
        """处理simulation结果，等待完成并提取alpha_id"""
        import asyncio
        import time
        
        self.logger.info(f"处理Simulation结果，数量: {len(batch_items)}")
        
        completed_factors = []
        pending_simulations = []
        
        # 分离已完成和待处理的simulation
        for item, factor in batch_items:
            if isinstance(item, str) and item.startswith('http'):
                # 这是simulation URL，需要等待完成
                pending_simulations.append((item, factor))
            else:
                # 这是alpha_id，已经完成
                try:
                    alpha_response = self.wqb_session.locate_alpha(item)
                    if alpha_response.ok:
                        alpha_data = alpha_response.json()
                        self._update_factor_performance(factor, alpha_data)
                        factor.status = FactorStatus.SIMULATED
                        completed_factors.append(factor)
                        self.logger.info(f"因子 {factor.id} Alpha验证成功，ID: {item}")
                    else:
                        self.logger.error(f"Alpha {item} 验证失败: HTTP {alpha_response.status_code}")
                        factor.status = FactorStatus.SIMULATION_FAILED
                except Exception as e:
                    self.logger.error(f"验证Alpha {item} 时出错: {e}")
                    factor.status = FactorStatus.SIMULATION_FAILED
        
        # 等待pending simulations完成
        if pending_simulations:
            self.logger.info(f"等待 {len(pending_simulations)} 个Simulation完成...")
            completed_from_pending = await self._wait_for_simulations(pending_simulations)
            completed_factors.extend(completed_from_pending)
        
        success_rate = len(completed_factors) / len(batch_items) * 100 if batch_items else 0
        self.logger.info(f"Simulation处理完成：成功 {len(completed_factors)} 个，失败 {len(batch_items) - len(completed_factors)} 个 (成功率: {success_rate:.1f}%)")
        
        return completed_factors
    
    async def _wait_for_simulations(self, pending_simulations: List[tuple], max_wait_time: int = 300) -> List[FactorSchema]:
        """等待simulation完成并提取alpha_id"""
        import asyncio
        import time
        
        completed_factors = []
        pending = dict(pending_simulations)  # {simulation_url: factor}
        
        start_time = time.time()
        check_interval = 15
        
        while pending and (time.time() - start_time) < max_wait_time:
            completed_urls = []
            
            self.logger.info(f"检查 {len(pending)} 个Simulation状态...")
            
            for simulation_url, factor in list(pending.items()):
                try:
                    # 直接GET simulation URL获取状态
                    response = self.wqb_session.get(simulation_url)
                    
                    if response and response.ok:
                        result = response.json()
                        status = result.get('status', '').lower()
                        
                        if status == 'completed':
                            # 提取alpha_id
                            alpha_id = result.get('alpha_id') or result.get('alphaId')
                            if alpha_id:
                                # 验证alpha并获取数据
                                alpha_response = self.wqb_session.locate_alpha(alpha_id)
                                if alpha_response.ok:
                                    alpha_data = alpha_response.json()
                                    self._update_factor_performance(factor, alpha_data)
                                    factor.wqb_alpha_id = alpha_id
                                    factor.status = FactorStatus.SIMULATED
                                    completed_factors.append(factor)
                                    completed_urls.append(simulation_url)
                                    self.logger.info(f"因子 {factor.id} Simulation完成，Alpha ID: {alpha_id}")
                                else:
                                    self.logger.error(f"Alpha {alpha_id} 验证失败")
                                    factor.status = FactorStatus.SIMULATION_FAILED
                                    completed_urls.append(simulation_url)
                            else:
                                self.logger.error(f"Simulation完成但缺少alpha_id: {simulation_url}")
                                factor.status = FactorStatus.SIMULATION_FAILED
                                completed_urls.append(simulation_url)
                        elif status in ['failed', 'error']:
                            self.logger.error(f"Simulation失败: {simulation_url}")
                            factor.status = FactorStatus.SIMULATION_FAILED
                            completed_urls.append(simulation_url)
                        else:
                            self.logger.debug(f"Simulation仍在运行: {status}")
                    else:
                        self.logger.warning(f"无法获取Simulation状态: {simulation_url}")
                        
                except Exception as e:
                    self.logger.error(f"检查Simulation {simulation_url} 时出错: {e}")
                    continue
            
            # 移除已完成的simulation
            for url in completed_urls:
                pending.pop(url, None)
            
            if pending:
                elapsed = time.time() - start_time
                remaining_time = max_wait_time - elapsed
                self.logger.info(f"等待中，剩余: {len(pending)} 个Simulation，剩余时间: {remaining_time:.1f}秒")
                await asyncio.sleep(check_interval)
        
        # 处理超时的simulation
        if pending:
            self.logger.warning(f"等待超时，{len(pending)} 个Simulation未完成")
            for factor in pending.values():
                factor.status = FactorStatus.SIMULATION_FAILED
        
        return completed_factors
    
    async def _verify_all_simulations_on_platform(self, completed_factors: List[FactorSchema]) -> List[FactorSchema]:
        """验证所有完成的模拟记录在WQB平台上都有对应记录"""
        verified_factors = []
        
        self.logger.info(f"开始验证 {len(completed_factors)} 个因子的WQB平台记录...")
        
        for factor in completed_factors:
            if factor.wqb_alpha_id:
                # 验证alpha在平台上的存在性
                is_verified = await self._verify_alpha_on_platform(factor.wqb_alpha_id, factor.id)
                
                if is_verified:
                    verified_factors.append(factor)
                    self.logger.info(f"因子 {factor.id} 平台记录验证通过")
                else:
                    self.logger.error(f"因子 {factor.id} 平台记录验证失败，alpha ID: {factor.wqb_alpha_id}")
                    factor.status = FactorStatus.SIMULATION_FAILED
            else:
                self.logger.warning(f"因子 {factor.id} 缺少WQB alpha ID，无法验证平台记录")
                factor.status = FactorStatus.SIMULATION_FAILED
        
        success_rate = len(verified_factors) / len(completed_factors) * 100 if completed_factors else 0
        self.logger.info(f"平台记录验证完成: {len(verified_factors)}/{len(completed_factors)} ({success_rate:.1f}%) 成功")
        
        return verified_factors

    def _update_factor_performance(self, factor: FactorSchema, result: dict):
        """更新因子性能数据 - 清理版本，专注于正确提取性能指标"""
        try:
            # 优先从locate_alpha响应的is字段提取性能数据
            if 'is' in result and isinstance(result['is'], dict):
                perf_data = result['is']
                self.logger.info(f"从locate_alpha响应获取因子 {factor.id} 的性能数据")
                
                if factor.performance:
                    # 提取关键性能指标
                    factor.performance.fitness = float(perf_data.get('fitness', 0.0))
                    factor.performance.sharpe = float(perf_data.get('sharpe', 0.0))
                    factor.performance.turnover = float(perf_data.get('turnover', 0.0))
                    factor.performance.returns = float(perf_data.get('returns', 0.0))
                    
                    # 提取额外指标
                    factor.performance.max_drawdown = perf_data.get('drawdown', None)
                    factor.performance.margin = perf_data.get('margin', None)
                    
                    self.logger.info(f"因子 {factor.id} 性能更新成功:")
                    self.logger.info(f"  fitness={factor.performance.fitness:.4f}, sharpe={factor.performance.sharpe:.4f}")
                    self.logger.info(f"  turnover={factor.performance.turnover:.4f}, returns={factor.performance.returns:.4f}")
                    
                    if factor.performance.max_drawdown is not None:
                        self.logger.info(f"  pnl={perf_data.get('pnl', 0)}, drawdown={factor.performance.max_drawdown:.4f}")
                else:
                    self.logger.error(f"因子 {factor.id} 缺少performance对象")
            else:
                self.logger.warning(f"因子 {factor.id} 的响应中缺少is字段，可用字段: {list(result.keys())}")
                # 设置默认性能值
                if factor.performance:
                    factor.performance.fitness = 0.0
                    factor.performance.sharpe = 0.0
                    factor.performance.turnover = 0.0
                    factor.performance.returns = 0.0
                
        except Exception as e:
            self.logger.error(f"更新因子 {factor.id} 性能数据失败: {e}")
            # 确保性能对象存在并设置默认值
            if factor.performance:
                factor.performance.fitness = 0.0
                factor.performance.sharpe = 0.0
                factor.performance.turnover = 0.0
                factor.performance.returns = 0.0
    
    def _evaluate_and_save(self, factors: List[FactorSchema]) -> List[FactorSchema]:
        """评估和保存因子 - 同步版本"""
        if not factors:
            return []
        
        # 简单质量检查
        validated_factors = []
        for factor in factors:
            if factor.performance and factor.performance.fitness > 0.01:  # 简单阈值
                factor.status = FactorStatus.VALIDATED
                validated_factors.append(factor)
        
        self.logger.info(f"质量检查完成，通过: {len(validated_factors)}")
        
        # 保存结果
        if validated_factors:
            filename = f"factors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            save_path = self.data_manager.save_factors(validated_factors, filename)
            self.logger.info(f"因子已保存: {save_path}")
        
        return validated_factors
    
    async def cleanup(self):
        """清理资源 - 按WQB文档标准"""
        self.logger.info("清理资源...")
        # WQBSession按文档设计无需手动清理
        self.logger.info("清理完成")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WQB因子挖掘系统')
    return parser.parse_args()


async def main():
    """主函数 - 按WQB 0.2.5文档标准实现"""
    args = parse_arguments()
    
    system = WQBFactorMiningSystem()
    
    try:
        system.initialize()
        
        # 运行工作流 - 按WQB文档标准
        await system.run_workflow()
            
    except KeyboardInterrupt:
        system.logger.info("用户中断")
    except Exception as e:
        system.logger.error(f"执行失败: {e}")
        raise
    finally:
        await system.cleanup()


if __name__ == "__main__":
    print("WQB因子挖掘系统 - 按WQB 0.2.5文档标准实现")
    print("完全重写的模拟工作流，遵循WQB库设计原则")
    print()
    
    asyncio.run(main()) 