deepseek:
  api_key: ***********************************
  base_url: https://api.deepseek.com
  enabled: false
  max_retries: 2
  model: deepseek-reasoner
  timeout: 120.0
factor_cache:
  auto_update: true
  enabled: true
  max_factors_per_strategy: 300
  performance_tracking: true
  ttl_hours: 48
factor_generation:
  ideas_per_batch: 5
  max_daily_factors: 150
  mode: wqb_search
  strategies:
    fundamental:
      max_factors: 30
      weight: 0.4
    sentiment:
      max_factors: 30
      weight: 0.25
    technical:
      max_factors: 30
      weight: 0.35
logging:
  backup_count: 5
  file_rotation: true
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: DEBUG
  max_file_size: 10MB
optimization:
  max_combinations: 100
  max_param_value: 60
  min_param_value: 1
  optimization_timeout: 300
quality_criteria:
  max_turnover: 0.7
  min_fitness: 1.0
  min_sharpe: 1.25
  min_turnover: 0.01
  required_checks:
  - CONCENTRATED_WEIGHT
  - LOW_SUB_UNIVERSE_SHARPE
run_mode:
  default: wqb_search
simulation:
  batch_delay: 10.0
  batch_size: 3
  concurrency: 3
  rate_limit_delay: 15.0
  max_retries: 5
  retry_backoff_factor: 2.0
  decay: 4
  delay: 1
  neutralization: SUBINDUSTRY
  region: USA
  truncation: 0.08
  universe: TOP3000
storage:
  backup_enabled: true
  base_path: outputs
  cache_duration_hours: 24
  compression: true
  subdirs:
    archives: archives
    factors: results/factors
    logs: logs/system
    reports: reports/analysis
strategy_params:
  fundamental:
    common_lookbacks:
    - 5
    - 10
    - 20
    - 60
  sentiment:
    common_lookbacks:
    - 1
    - 3
    - 5
    - 15
  technical:
    common_lookbacks:
    - 3
    - 7
    - 14
    - 30
wqb:
  credentials_file: WQB/credential.txt
