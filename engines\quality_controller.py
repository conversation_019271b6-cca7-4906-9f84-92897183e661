# WQB质量控制引擎
# 脚本功能：基于WorldQuant Brain规则的因子质量验证和控制系统

import logging
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

from data.schemas import FactorSchema, PerformanceMetrics, QualityChecks, FactorStatus
from utils.helpers import ExpressionValidator

class QualityController:
    """智能质量控制引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 质量标准
        self.quality_criteria = config['quality_criteria']
        self.min_fitness = self.quality_criteria['min_fitness']
        self.min_sharpe = self.quality_criteria['min_sharpe']
        self.max_turnover = self.quality_criteria['max_turnover']
        self.min_turnover = self.quality_criteria['min_turnover']
        self.required_checks = self.quality_criteria['required_checks']
    
    def validate_factor_quality(self, factor: FactorSchema) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """全面验证因子质量"""
        self.logger.info(f"验证因子质量: {factor.id}")
        
        validation_result = {
            'passed': False,
            'score': 0.0,
            'checks': {
                'syntax_valid': False,
                'performance_meets_criteria': False,
                'quality_checks_passed': False,
                'economic_logic': False,
                'overfitting_risk': 'UNKNOWN'
            },
            'warnings': [],
            'recommendations': []
        }
        
        # 1. 语法验证
        is_valid, syntax_msg = ExpressionValidator.validate_syntax(factor.expression)
        validation_result['checks']['syntax_valid'] = is_valid
        if not is_valid:
            validation_result['warnings'].append(f"语法错误: {syntax_msg}")
            return False, validation_result
        
        # 2. 性能指标验证
        perf_passed = self._validate_performance_metrics(factor.performance)
        validation_result['checks']['performance_meets_criteria'] = perf_passed
        if not perf_passed:
            validation_result['warnings'].append("性能指标未达到最低标准")
        
        # 3. 质量检查验证
        quality_passed = factor.quality_checks.all_passed(self.required_checks)
        validation_result['checks']['quality_checks_passed'] = quality_passed
        if not quality_passed:
            validation_result['warnings'].append("质量检查未通过")
        
        # 4. 经济学逻辑验证
        economic_logic = self._validate_economic_logic(factor)
        validation_result['checks']['economic_logic'] = economic_logic['valid']
        if not economic_logic['valid']:
            validation_result['warnings'].extend(economic_logic['warnings'])
        
        # 5. 过拟合风险评估
        overfitting_risk = self._assess_overfitting_risk(factor)
        validation_result['checks']['overfitting_risk'] = overfitting_risk['risk_level']
        if overfitting_risk['risk_level'] != 'LOW':
            validation_result['warnings'].extend(overfitting_risk['warnings'])
            validation_result['recommendations'].extend(overfitting_risk['recommendations'])
        
        # 计算综合评分
        validation_result['score'] = self._calculate_quality_score(factor, validation_result['checks'])
        
        # 判断是否通过
        validation_result['passed'] = (
            validation_result['checks']['syntax_valid'] and
            validation_result['checks']['performance_meets_criteria'] and
            validation_result['checks']['quality_checks_passed'] and
            validation_result['checks']['economic_logic'] and
            validation_result['checks']['overfitting_risk'] in ['LOW', 'MEDIUM']
        )
        
        return validation_result['passed'], validation_result
    
    def _validate_performance_metrics(self, performance: PerformanceMetrics) -> bool:
        """验证性能指标"""
        return (
            performance.fitness >= self.min_fitness and
            performance.sharpe >= self.min_sharpe and
            self.min_turnover <= performance.turnover <= self.max_turnover
        )
    
    def _validate_economic_logic(self, factor: FactorSchema) -> Dict[str, Any]:
        """验证经济学逻辑"""
        result = {
            'valid': True,
            'warnings': [],
            'logic_score': 0.8  # 默认分数
        }
        
        expression = factor.expression.lower()
        
        # 检查常见的逻辑问题
        logic_warnings = []
        
        # 1. 检查是否过度复杂
        operator_count = len(ExpressionValidator._extract_operators(factor.expression))
        if operator_count > 8:
            logic_warnings.append("表达式过于复杂，可能存在过拟合风险")
            result['logic_score'] -= 0.2
        
        # 2. 检查是否使用了合理的时间窗口
        parameters = ExpressionValidator.extract_parameters(factor.expression)
        if parameters:
            max_param = max(parameters)
            if max_param > 252:  # 超过一年
                logic_warnings.append("时间窗口过长，可能影响信号时效性")
                result['logic_score'] -= 0.1
            elif max_param < 2:
                logic_warnings.append("时间窗口过短，可能包含过多噪音")
                result['logic_score'] -= 0.1
        
        result['warnings'] = logic_warnings
        if result['logic_score'] < 0.5:
            result['valid'] = False
        
        return result
    
    def _assess_overfitting_risk(self, factor: FactorSchema) -> Dict[str, Any]:
        """评估过拟合风险"""
        risk_assessment = {
            'risk_level': 'LOW',
            'warnings': [],
            'recommendations': [],
            'risk_score': 0.0
        }
        
        # 1. Turnover风险
        if factor.performance.turnover > 0.5:
            risk_assessment['risk_score'] += 0.3
            risk_assessment['warnings'].append("Turnover过高，存在过拟合风险")
        
        # 2. Fitness/Sharpe比例异常
        if factor.performance.fitness > 0 and factor.performance.sharpe > 0:
            ratio = factor.performance.fitness / factor.performance.sharpe
            if ratio > 2.5:
                risk_assessment['risk_score'] += 0.4
                risk_assessment['warnings'].append("Fitness与Sharpe比例异常")
        
        # 确定风险等级
        if risk_assessment['risk_score'] >= 0.7:
            risk_assessment['risk_level'] = 'HIGH'
        elif risk_assessment['risk_score'] >= 0.4:
            risk_assessment['risk_level'] = 'MEDIUM'
        else:
            risk_assessment['risk_level'] = 'LOW'
        
        return risk_assessment
    
    def _calculate_quality_score(self, factor: FactorSchema, checks: Dict[str, Any]) -> float:
        """计算综合质量评分"""
        score = 0.0
        
        # 基础分数
        if checks['syntax_valid']:
            score += 20
        if checks['performance_meets_criteria']:
            score += 30
        if checks['quality_checks_passed']:
            score += 25
        if checks['economic_logic']:
            score += 15
        
        # 过拟合风险调整
        if checks['overfitting_risk'] == 'LOW':
            score += 10
        elif checks['overfitting_risk'] == 'MEDIUM':
            score += 5
        
        return max(0.0, min(100.0, score))
    
    def batch_validate_factors(self, factors: List[FactorSchema]) -> Dict[str, Any]:
        """批量验证因子质量"""
        self.logger.info(f"开始批量验证 {len(factors)} 个因子")
        
        passed_factors = []
        failed_factors = []
        validation_details = []
        
        for factor in factors:
            passed, details = self.validate_factor_quality(factor)
            
            if passed:
                passed_factors.append({
                    'factor': factor,
                    'score': details['score'],
                    'details': details
                })
            else:
                failed_factors.append({
                    'factor': factor,
                    'score': details['score'],
                    'warnings': details['warnings'],
                    'details': details
                })
            
            validation_details.append(details)
        
        # 计算统计信息
        total_count = len(factors)
        passed_count = len(passed_factors)
        failed_count = len(failed_factors)
        pass_rate = passed_count / total_count if total_count > 0 else 0
        
        validation_stats = {
            'total_factors': total_count,
            'passed_count': passed_count,
            'failed_count': failed_count,
            'pass_rate': pass_rate,
            'average_score': sum(d['score'] for d in validation_details) / total_count if total_count > 0 else 0
        }
        
        self.logger.info(f"批量验证完成: {passed_count}/{total_count} 通过 (通过率: {pass_rate:.2%})")
        
        return {
            'passed_factors': passed_factors,
            'failed_factors': failed_factors,
            'validation_stats': validation_stats,
            'validation_details': validation_details
        }
    
    def generate_quality_report(self, validation_results: Dict[str, Any]) -> str:
        """生成质量验证报告"""
        stats = validation_results['validation_stats']
        passed_factors = validation_results['passed_factors']
        failed_factors = validation_results['failed_factors']
        
        report = f"""# 因子质量验证报告

## 验证概览
- 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总因子数: {stats['total_factors']}
- 通过因子数: {stats['passed_count']}
- 失败因子数: {stats['failed_count']}
- 通过率: {stats['pass_rate']:.2%}
- 平均分数: {stats['average_score']:.2f}

## 通过因子详情
"""
        
        for item in passed_factors[:10]:  # 只显示前10个
            factor = item['factor']
            report += f"- {factor.id}: {factor.expression} (评分: {item['score']:.1f})\n"
        
        if len(passed_factors) > 10:
            report += f"... 还有 {len(passed_factors) - 10} 个通过的因子\n"
        
        report += "\n## 失败因子分析\n"
        
        for item in failed_factors[:5]:  # 只显示前5个失败案例
            factor = item['factor']
            report += f"- {factor.id}: {factor.expression}\n"
            report += f"  评分: {item['score']:.1f}\n"
            report += f"  问题: {'; '.join(item['warnings'])}\n\n"
        
        if len(failed_factors) > 5:
            report += f"... 还有 {len(failed_factors) - 5} 个失败的因子\n"
        
        return report 