# WQB系统辅助工具
# 脚本功能：配置加载、表达式验证、数据转换等工具函数

import yaml
import json
import re
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

class ConfigLoader:
    """配置文件加载器"""
    
    @staticmethod
    def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logging.info(f"配置文件已加载: {config_path}")
            return config
        except FileNotFoundError:
            logging.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            logging.error(f"配置文件格式错误: {e}")
            raise
    


class ExpressionValidator:
    """Alpha表达式验证器"""
    
    # WorldQuant Brain运算符列表
    VALID_OPERATORS = {
        # Arithmetic
        'abs', 'add', 'densify', 'divide', 'inverse', 'log', 'max', 'min', 
        'multiply', 'power', 'reverse', 'sign', 'signed_power', 'sqrt', 'subtract',
        
        # Logical  
        'and', 'if_else', 'is_nan', 'not', 'or',
        
        # Time Series
        'days_from_last_change', 'hump', 'kth_element', 'last_diff_value',
        'ts_arg_max', 'ts_arg_min', 'ts_av_diff', 'ts_backfill', 'ts_corr',
        'ts_count_nans', 'ts_covariance', 'ts_decay_linear', 'ts_delay',
        'ts_delta', 'ts_mean', 'ts_product', 'ts_quantile', 'ts_rank',
        'ts_regression', 'ts_scale', 'ts_std_dev', 'ts_step', 'ts_sum', 'ts_zscore',
        
        # Cross Sectional
        'normalize', 'quantile', 'rank', 'scale', 'winsorize', 'zscore',
        
        # Vector
        'vec_avg', 'vec_sum',
        
        # Transformational
        'bucket', 'trade_when',
        
        # Group
        'group_backfill', 'group_mean', 'group_neutralize', 'group_rank',
        'group_scale', 'group_zscore',
        
        # Constants and groups
        'INDUSTRY', 'SECTOR', 'SUBINDUSTRY', 'MARKET', 'COUNTRY'
    }
    
    @classmethod
    def validate_syntax(cls, expression: Union[str, dict, Any]) -> tuple[bool, str]:
        """验证表达式语法"""
        # 类型检查和转换
        if isinstance(expression, dict):
            # 如果是字典，尝试提取expression字段
            expression = expression.get('expression', str(expression))
        elif not isinstance(expression, str):
            # 如果不是字符串，转换为字符串
            expression = str(expression)
        
        if not expression or not expression.strip():
            return False, "表达式不能为空"
        
        # 检查括号匹配
        if not cls._check_parentheses(expression):
            return False, "括号不匹配"
        
        # 检查运算符
        used_operators = cls._extract_operators(expression)
        invalid_operators = used_operators - cls.VALID_OPERATORS
        if invalid_operators:
            return False, f"无效运算符: {', '.join(invalid_operators)}"
        
        return True, "语法验证通过"
    
    @staticmethod
    def _check_parentheses(expression: str) -> bool:
        """检查括号是否匹配"""
        count = 0
        for char in expression:
            if char == '(':
                count += 1
            elif char == ')':
                count -= 1
                if count < 0:
                    return False
        return count == 0
    
    @classmethod
    def _extract_operators(cls, expression: str) -> set:
        """提取表达式中的运算符"""
        # 匹配函数调用模式: function_name(
        pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        matches = re.findall(pattern, expression)
        return set(matches)
    
    @staticmethod
    def extract_parameters(expression: str) -> List[int]:
        """提取表达式中的数字参数"""
        pattern = r'\b(\d+)\b'
        matches = re.findall(pattern, expression)
        return [int(match) for match in matches]

class DataConverter:
    """数据转换工具"""
    
    @staticmethod
    def to_multi_alphas(alphas: List[Dict], batch_size: int = 10) -> List[List[Dict]]:
        """将alpha列表转换为批次"""
        batches = []
        for i in range(0, len(alphas), batch_size):
            batches.append(alphas[i:i + batch_size])
        return batches
    
    @staticmethod
    def normalize_metrics(metrics: Dict[str, float]) -> Dict[str, float]:
        """标准化性能指标"""
        normalized = {}
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                normalized[key] = round(float(value), 6)
            else:
                normalized[key] = value
        return normalized
    
    @staticmethod
    def create_timestamp() -> str:
        """创建时间戳"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def safe_filename(text: str, max_length: int = 50) -> str:
        """创建安全的文件名"""
        # 移除非法字符
        safe_text = re.sub(r'[<>:"/\\|?*]', '_', text)
        # 截断长度
        if len(safe_text) > max_length:
            safe_text = safe_text[:max_length]
        return safe_text

class PathManager:
    """路径管理工具"""
    
    def __init__(self, base_dir: str = "outputs"):
        self.base_dir = Path(base_dir)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        subdirs = [
            "results/factors",
            "reports/analysis", 
            "logs/system",
            "archives"
        ]
        
        for subdir in subdirs:
            dir_path = self.base_dir / subdir
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_factor_path(self, filename: str) -> Path:
        """获取因子文件路径"""
        return self.base_dir / "results" / "factors" / filename
    
    def get_report_path(self, filename: str) -> Path:
        """获取报告文件路径"""
        return self.base_dir / "reports" / "analysis" / filename
    
    def get_log_path(self, filename: str) -> Path:
        """获取日志文件路径"""
        return self.base_dir / "logs" / "system" / filename
    
    def get_archive_path(self, filename: str) -> Path:
        """获取归档文件路径"""
        return self.base_dir / "archives" / filename 