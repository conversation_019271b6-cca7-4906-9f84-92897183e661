# WQB因子挖掘系统

基于WorldQuant Brain (WQB) Python库的智能因子挖掘系统，集成DeepSeek AI和高并发技术，实现自动化的量化因子研究和开发。

## 功能特性

- 🤖 **AI驱动因子生成**：集成DeepSeek大语言模型，智能生成多策略类型因子
- ⚡ **高并发模拟**：利用wqb库并发能力，支持最多10个同时模拟
- 🔍 **智能质量控制**：基于WorldQuant规则的多维度质量验证
- 🎯 **参数优化引擎**：自动化参数搜索和防过拟合检测
- 📊 **数据管理系统**：完整的数据存储、缓存和备份管理

## 快速开始

### 1. 环境准备

确保已安装Python 3.8+，然后安装依赖：

```bash
# 在项目根目录下运行
pip install -r requirements.txt

# 如果遇到权限问题，可以使用：
pip install --user -r requirements.txt

# 或者使用虚拟环境（推荐）：
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

**注意**：如果遇到 `ModuleNotFoundError: No module named 'yaml'` 错误，请确保运行了上述安装命令。

### 2. 配置设置

1. **配置WQB认证信息**：
   编辑 `credential.txt` 文件，格式为：
   ```
   ["<EMAIL>", "your_password"]
   ```

2. **配置DeepSeek API**：
   在 `config.yaml` 中设置：
   ```yaml
   ai:
     api_key: "your_deepseek_api_key"
   ```

3. **调整系统配置**：
   根据需要修改 `config.yaml` 中的并发数、质量标准等参数。

### 3. 运行系统

**启动完整因子挖掘工作流**：
```bash
python main.py
```

系统将自动执行以下阶段：
1. 因子生成阶段 - AI驱动的多策略因子生成
2. 质量控制阶段 - 多维度质量验证和筛选
3. 参数优化阶段 - 智能参数搜索和优化
4. 最终验证阶段 - 最终质量检查和结果保存

## 系统架构

```
WQB因子挖掘系统/
├── engines/           # 核心引擎模块
│   ├── factor_generator.py      # AI因子生成引擎
│   ├── parameter_optimizer.py   # 参数优化引擎
│   └── quality_controller.py    # 质量控制引擎
├── data/              # 数据管理模块
│   ├── schemas.py               # 数据模型定义
│   └── data_manager.py          # 数据管理器
├── strategies/        # 策略模板模块
│   ├── fundamental.py           # 基本面策略模板
│   ├── technical.py             # 技术分析策略模板
│   └── sentiment.py             # 情绪策略模板
├── utils/             # 工具模块
│   └── helpers.py               # 工具函数
├── config.yaml        # 配置文件
├── main.py           # 主程序入口
└── README.md         # 使用说明
```

## 工作流程

### 1. 因子生成阶段
- 使用AI生成多种策略类型的因子表达式
- 支持基本面、技术分析、情绪三大策略类型
- 内置模板库和智能提示生成

### 2. 质量控制阶段
- 语法验证：检查表达式合法性
- 性能验证：fitness ≥ 1.0, sharpe ≥ 1.25等标准
- 经济学逻辑验证：确保策略合理性
- 过拟合风险评估：多维度风险检测

### 3. 参数优化阶段
- 智能参数搜索和组合生成
- 并发测试多个参数变体
- 防过拟合检测和综合评分

### 4. 最终验证保存
- 最终质量检查和状态更新
- 数据保存和Excel报告生成
- 工作流报告和统计分析

## 配置说明

### 核心配置项

```yaml
# WQB连接配置
wqb:
  base_url: "https://platform.worldquantbrain.com"
  credentials_file: "credential.txt"

# 并发设置
concurrency:
  max_concurrent_simulations: 8  # 最大并发数
  batch_size: 5                  # 批次大小
  simulation_delay: 2.0          # 延迟时间

# 质量标准
quality_criteria:
  min_fitness: 1.0      # 最低fitness要求
  min_sharpe: 1.25      # 最低sharpe要求
  max_turnover: 0.7     # 最大turnover限制
```

### 策略权重配置

```yaml
factor_generation:
  strategies:
    fundamental:
      weight: 0.40      # 基本面策略权重40%
      max_factors: 50
    technical:
      weight: 0.35      # 技术分析权重35%
      max_factors: 50
    sentiment:
      weight: 0.25      # 情绪策略权重25%
      max_factors: 30
```

## 使用示例

### 基本API使用

```python
import asyncio
from engines.factor_generator import FactorGenerator
from data.schemas import StrategyType
from utils.helpers import ConfigLoader

async def generate_factors_example():
    # 加载配置
    config = ConfigLoader.load_config()
    
    # 创建因子生成器
    generator = FactorGenerator(config)
    
    # 生成技术分析因子
    factors = await generator.generate_factors(
        count=10, 
        strategy_type=StrategyType.TECHNICAL
    )
    
    for factor in factors:
        print(f"因子: {factor.expression}")

# 运行示例
asyncio.run(generate_factors_example())
```

### 质量控制示例

```python
from engines.quality_controller import QualityController

# 初始化质量控制器
controller = QualityController(config)

# 验证因子质量
for factor in factors:
    passed, result = controller.validate_factor_quality(factor)
    print(f"因子 {factor.id}: {'通过' if passed else '未通过'}")
    print(f"  质量评分: {result['score']:.2f}")
    if result['warnings']:
        print(f"  警告: {', '.join(result['warnings'])}")
```

### 数据管理示例

```python
from data.data_manager import DataManager

# 初始化数据管理器
manager = DataManager(config)

# 保存因子
filepath = manager.save_factors(factors, "my_factors.json")
print(f"因子已保存到: {filepath}")

# 导出Excel报告
excel_path = manager.export_to_excel(factors)
print(f"Excel报告: {excel_path}")

# 数据备份
backup_path = manager.backup_data("backup_20250106")
print(f"数据已备份到: {backup_path}")
```

## WorldQuant Brain集成

### 支持的运算符

- **算术运算**：add, subtract, multiply, divide, power, log等
- **逻辑运算**：and, or, not, if_else等
- **时间序列**：ts_mean, ts_sum, ts_rank, ts_regression等
- **截面运算**：rank, scale, zscore, normalize等
- **分组运算**：group_neutralize, group_rank等

### 开发规则集成

系统集成了WorldQuant Brain的开发提示和最佳实践：

- D0/D1策略差异化处理
- 中性化设置建议和应用
- 回归分析的正确使用
- 防过拟合技巧和检测
- 容量优化方法

## 输出文件

系统运行后会在 `data/` 目录下生成：

```
data/
├── factors/          # 因子数据文件(.json)
├── results/          # 模拟结果文件(.json)
├── reports/          # 报告文件(.xlsx, .md)
├── cache/           # 缓存文件(.pkl)
└── backups/         # 备份文件
```

## 性能优化

### 并发配置优化
- 根据网络和计算资源调整 `max_concurrent_simulations`
- 适当设置 `simulation_delay` 避免API限制
- 合理配置 `batch_size` 平衡效率和稳定性

### 缓存策略
- 系统自动缓存AI生成结果和WQB响应
- 定期清理过期缓存释放空间
- 支持手动缓存管理和清理

### 内存管理
- 大批量处理时自动分批执行
- 及时释放不需要的数据结构
- 异步编程避免阻塞操作

## 故障排除

### 常见问题

1. **认证失败**
   - 检查 `credential.txt` 文件格式
   - 确认WQB账户有效性
   - 验证网络连接

2. **API限制**
   - 增加 `simulation_delay` 参数
   - 减少 `max_concurrent_simulations`
   - 检查WQB账户配额

3. **内存不足**
   - 减少 `batch_size` 和并发数
   - 增加系统可用内存
   - 清理不必要的缓存文件

4. **生成质量差**
   - 检查DeepSeek API配置
   - 调整质量标准阈值
   - 更新AI提示模板

### 日志分析

系统日志保存在 `logs/` 目录：
- `wqb_mining_YYYYMMDD.log`：主要系统日志
- 包含详细的执行过程和错误信息
- 支持不同级别的日志输出

## 扩展开发

### 添加新策略类型

1. 在 `data/schemas.py` 中扩展 `StrategyType` 枚举
2. 在 `strategies/` 目录创建策略模板文件
3. 在 `engines/factor_generator.py` 中添加生成逻辑
4. 更新配置文件中的策略权重

### 自定义质量检查

1. 在 `QualityController` 中添加新验证方法
2. 扩展 `QualityChecks` 数据模型
3. 更新配置中的 `required_checks` 列表

### 新增运算符支持

1. 在 `utils/helpers.py` 的 `ExpressionValidator` 类中扩展
2. 添加相应的语法验证规则
3. 更新AI提示模板以使用新运算符

## 最佳实践

### 因子开发
- 遵循经济学逻辑，避免纯数据挖掘
- 使用适当的中性化处理降低市场风险
- 控制表达式复杂度防止过拟合
- 定期进行样本外验证

### 系统运维
- 定期备份重要数据和配置
- 监控系统性能和资源使用
- 及时清理过期缓存和临时文件
- 保持API密钥和认证信息安全

### 质量控制
- 设定合理的质量阈值标准
- 重视过拟合风险检测和控制
- 进行多维度验证确保稳健性
- 记录优化历史便于追踪

## 许可证

MIT License - 详见 LICENSE 文件

## 支持与贡献

- 🐛 **Bug报告**：请通过GitHub Issues提交
- 💡 **功能建议**：欢迎提出改进建议
- 🤝 **贡献代码**：Fork项目并提交Pull Request
- 📖 **文档改进**：帮助完善文档和示例

## 更新日志

### v1.0.0 (2025-01-06)
- 初始版本发布
- 实现核心因子生成和优化功能
- 集成WorldQuant Brain规则和最佳实践
- 支持多策略类型和高并发处理
- 完整的质量控制和数据管理系统

---

**联系方式**：如有问题或建议，请通过GitHub Issues联系。 